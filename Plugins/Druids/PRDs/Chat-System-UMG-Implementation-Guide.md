# Chat System UMG Refactor - Implementation Guide

## Document Information
- **Document Version**: 1.0
- **Created Date**: 2024-12-19
- **Related Documents**: Chat-System-UMG-Refactor-PRD.md, Chat-System-UMG-Technical-Specifications.md
- **Project**: Druids Plugin for Unreal Engine

## Implementation Strategy

### Phase 1: Foundation Setup (Week 1)

#### 1.1 Create Base UMG Classes
```cpp
// File: Source/DruidsSage/Interaction/SageUI/Public/UMG/DruidsChatWidgetBase.h
UCLASS(Abstract, BlueprintType, Blueprintable)
class SAGEUI_API UDruidsChatWidgetBase : public UUserWidget
{
    GENERATED_BODY()

public:
    // Common functionality for all chat widgets
    UFUNCTION(BlueprintImplementableEvent, Category = "Druids Chat")
    void OnStyleChanged(const FDruidsChatStyle& NewStyle);
    
    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    virtual void ApplyStyle(const FDruidsChatStyle& Style);

protected:
    UPROPERTY(BlueprintReadOnly, Category = "Style")
    FDruidsChatStyle CurrentStyle;
};
```

#### 1.2 Create Widget Factory System
```cpp
// File: Source/DruidsSage/Interaction/SageUI/Public/UMG/DruidsChatWidgetFactory.h
UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsChatWidgetFactory : public UObject
{
    GENERATED_BODY()

public:
    // Factory methods with Blueprint override support
    UFUNCTION(BlueprintCallable, Category = "Widget Factory")
    UDruidsSageChatShell* CreateChatShell(UObject* Outer = nullptr);
    
    UFUNCTION(BlueprintCallable, Category = "Widget Factory")
    UDruidsSageChatView* CreateChatView(UObject* Outer = nullptr);
    
    UFUNCTION(BlueprintCallable, Category = "Widget Factory")
    UUserWidget* CreateChatItem(EDruidsSageChatRole Role, UObject* Outer = nullptr);

protected:
    // Template class references
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSubclassOf<UDruidsSageChatShell> ChatShellClass;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSubclassOf<UDruidsSageChatView> ChatViewClass;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSubclassOf<UUserWidget> AssistantChatItemClass;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSubclassOf<UUserWidget> SimpleChatItemClass;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSubclassOf<UUserWidget> ActionRequestItemClass;
    
    // Internal helper methods
    void LoadWidgetClasses();
    bool ValidateWidgetClass(UClass* WidgetClass, UClass* ExpectedBaseClass);
};
```

### Phase 2: Widget Conversion (Weeks 2-3)

#### 2.1 Convert SDruidsSageChatShell to UMG

**Step 1: Create UDruidsSageChatShell Class**
```cpp
// File: Source/DruidsSage/Interaction/SageUI/Public/UMG/DruidsSageChatShell.h
UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageChatShell : public UDruidsChatWidgetBase
{
    GENERATED_BODY()

public:
    UDruidsSageChatShell(const FObjectInitializer& ObjectInitializer);
    
    // Delegate for message sending (replaces Slate delegate)
    UPROPERTY(BlueprintAssignable, Category = "Chat Events")
    FOnMessageSending OnMessageSending;
    
    // Public interface methods
    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    void SetChatRequestHandler(const TScriptInterface<IChatRequestHandler>& Handler);
    
    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    void SetExtensionDelegator(const TScriptInterface<ISageExtensionDelegator>& Delegator);
    
    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    UDruidsSageChatView* GetCurrentChatView() const { return CurrentChatView; }

protected:
    // Widget bindings (set in Blueprint Designer)
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UListView* SessionListView;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UBorder* ChatViewContainer;
    
    // Internal state
    UPROPERTY(BlueprintReadOnly, Category = "Chat Shell")
    UDruidsSageChatView* CurrentChatView;
    
    UPROPERTY()
    TScriptInterface<IChatRequestHandler> ChatRequestHandler;
    
    UPROPERTY()
    TScriptInterface<ISageExtensionDelegator> ExtensionDelegator;
    
    // Session management
    UPROPERTY(BlueprintReadOnly, Category = "Sessions")
    TArray<FString> ChatSessions;
    
    // Blueprint events for customization
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell Events")
    void OnChatSessionChanged(const FString& SessionName);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell Events")
    UDruidsSageChatView* CreateChatViewWidget();
    
    // Native overrides
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;
    
    // Session management methods
    UFUNCTION(BlueprintCallable, Category = "Sessions")
    void InitializeChatSessions();
    
    UFUNCTION(BlueprintCallable, Category = "Sessions")
    void SwitchToSession(const FString& SessionName);
    
    UFUNCTION()
    void OnSessionSelectionChanged(UObject* Item);
};
```

**Step 2: Create Default Blueprint Template**
- Create `WBP_DruidsChatShell_Default` in `/Game/Druids/UI/Templates/`
- Set up basic layout with UListView for sessions and UBorder for chat view
- Bind widget references in Blueprint
- Implement `CreateChatViewWidget` event to return appropriate chat view

#### 2.2 Convert SDruidsSageChatView to UMG

**Step 1: Create UDruidsSageChatView Class**
```cpp
// File: Source/DruidsSage/Interaction/SageUI/Public/UMG/DruidsSageChatView.h
UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageChatView : public UDruidsChatWidgetBase
{
    GENERATED_BODY()

public:
    UDruidsSageChatView(const FObjectInitializer& ObjectInitializer);
    
    // Event delegates
    UPROPERTY(BlueprintAssignable, Category = "Chat Events")
    FOnMessageSending OnMessageSending;
    
    // Public interface
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SendMessage(const FString& MessageText, EDruidsSageChatRole Role = EDruidsSageChatRole::User);
    
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void ClearChat();
    
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SetTabContext(const FString& Context, const FString& DisplayMessage);
    
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SetBlueprintContext(const FString& Context, const FString& DisplayMessage);

protected:
    // Widget bindings
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UScrollBox* ChatScrollBox;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UVerticalBox* ChatMessageContainer;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UMultiLineEditableTextBox* MessageInputBox;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UButton* SendButton;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UButton* ClearButton;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UVerticalBox* ContextChipContainer;
    
    // Internal state
    UPROPERTY(BlueprintReadOnly, Category = "Chat View")
    FString SessionID;
    
    UPROPERTY(BlueprintReadOnly, Category = "Chat View")
    TArray<UUserWidget*> ChatItems;
    
    // Context information
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString CurrentTabContext;
    
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString CurrentBlueprintContext;
    
    // Blueprint events
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Events")
    UUserWidget* CreateChatItem(EDruidsSageChatRole Role, const FDruidsSageChatMessage& Message);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Events")
    void OnMessageAdded(UUserWidget* ChatItem);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Events")
    void OnContextUpdated(const FString& Context, const FString& DisplayMessage);
    
    // Native overrides
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;
    
    // Button event handlers
    UFUNCTION()
    void OnSendButtonClicked();
    
    UFUNCTION()
    void OnClearButtonClicked();
    
    UFUNCTION()
    void OnMessageInputTextChanged(const FText& Text);
    
    // Internal methods
    void AddChatItem(UUserWidget* ChatItem);
    void LoadChatHistory();
    void SaveChatHistory();
};
```

### Phase 3: Chat Item Widget Conversion (Week 3)

#### 3.1 Create Base Chat Item Interface
```cpp
// File: Source/DruidsSage/Interaction/SageUI/Public/UMG/DruidsChatItemInterface.h
UINTERFACE(BlueprintType)
class SAGEUI_API UDruidsChatItemInterface : public UInterface
{
    GENERATED_BODY()
};

class SAGEUI_API IDruidsChatItemInterface
{
    GENERATED_BODY()

public:
    // Required interface methods
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
    void SetMessageContent(const FDruidsSageChatMessage& Message);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
    void UpdateFromStreamingContent(const FString& PartialContent);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
    EDruidsSageChatRole GetMessageRole() const;
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
    void OnActionButtonClicked(const FString& ActionData);
    
    // Optional customization events
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
    void OnStyleApplied(const FDruidsChatStyle& Style);
};
```

#### 3.2 Create Chat Item Base Class
```cpp
// File: Source/DruidsSage/Interaction/SageUI/Public/UMG/DruidsChatItemBase.h
UCLASS(Abstract, BlueprintType, Blueprintable)
class SAGEUI_API UDruidsChatItemBase : public UUserWidget, public IDruidsChatItemInterface
{
    GENERATED_BODY()

public:
    UDruidsChatItemBase(const FObjectInitializer& ObjectInitializer);
    
    // Interface implementation (can be overridden in Blueprint)
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Chat Item")
    void SetMessageContent(const FDruidsSageChatMessage& Message);
    virtual void SetMessageContent_Implementation(const FDruidsSageChatMessage& Message);
    
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Chat Item")
    void UpdateFromStreamingContent(const FString& PartialContent);
    virtual void UpdateFromStreamingContent_Implementation(const FString& PartialContent);
    
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Chat Item")
    EDruidsSageChatRole GetMessageRole() const;
    virtual EDruidsSageChatRole GetMessageRole_Implementation() const;

protected:
    // Common widget bindings
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UTextBlock* RoleLabel;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class URichTextBlock* MessageContent;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UBorder* MessageContainer;
    
    // Message data
    UPROPERTY(BlueprintReadOnly, Category = "Message Data")
    FDruidsSageChatMessage CurrentMessage;
    
    UPROPERTY(BlueprintReadOnly, Category = "Message Data")
    EDruidsSageChatRole MessageRole;
    
    // Style application
    virtual void NativeConstruct() override;
    void ApplyRoleBasedStyling();
};
```

### Phase 4: Integration and Testing (Week 4)

#### 4.1 Update SageMainModule
```cpp
// File: Source/DruidsSage/SageMain/Private/SageMainModule.cpp
void FSageMainModule::OnPostEngineInit()
{
    if (FModuleManager::Get().IsModuleLoaded("DruidsSageEditorModule"))
    {
        FDruidsSageEditorModule& EditorModule = FModuleManager::GetModuleChecked<FDruidsSageEditorModule>("DruidsSageEditorModule");
        
        // Bind UMG widget creation instead of Slate
        EditorModule.OnCreateChatShell.BindLambda([this]() -> TSharedPtr<SCompoundWidget> {
            // Get widget factory from subsystem
            UDruidsChatWidgetManager* WidgetManager = UDruidsChatWidgetManager::Get(GEngine);
            if (!WidgetManager)
            {
                return nullptr;
            }
            
            UDruidsChatWidgetFactory* Factory = WidgetManager->GetWidgetFactory();
            if (!Factory)
            {
                return nullptr;
            }
            
            // Create UMG chat shell
            UDruidsSageChatShell* ChatShell = Factory->CreateChatShell();
            if (!ChatShell)
            {
                return nullptr;
            }
            
            // Set up handlers
            ChatShell->SetChatRequestHandler(MakeShared<ChatRequestHandler_V2>());
            ChatShell->SetExtensionDelegator(MakeShared<USageExtensionDelegator>());
            
            // Wrap UMG widget in Slate widget for editor integration
            return SNew(SBox)
                .HAlign(HAlign_Fill)
                .VAlign(VAlign_Fill)
                [
                    ChatShell->TakeWidget()
                ];
        });
    }
}
```

#### 4.2 Create Migration Utility
```cpp
// File: Source/DruidsSage/Interaction/SageUI/Public/UMG/DruidsChatMigrationUtility.h
UCLASS(BlueprintType)
class SAGEUI_API UDruidsChatMigrationUtility : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // Migration helper functions
    UFUNCTION(BlueprintCallable, Category = "Migration", CallInEditor = true)
    static bool MigrateChatHistoryToUMG();
    
    UFUNCTION(BlueprintCallable, Category = "Migration", CallInEditor = true)
    static bool ValidateUMGWidgetSetup();
    
    UFUNCTION(BlueprintCallable, Category = "Migration", CallInEditor = true)
    static void CreateDefaultWidgetTemplates();
    
    UFUNCTION(BlueprintCallable, Category = "Migration", CallInEditor = true)
    static TArray<FString> GetMigrationIssues();
};
```

## Blueprint Template Creation Guide

### Default Template Structure
```
/Game/Druids/UI/Templates/
├── WBP_DruidsChatShell_Default.uasset
├── WBP_DruidsChatView_Default.uasset
├── WBP_AssistantChatItem_Default.uasset
├── WBP_SimpleChatItem_Default.uasset
├── WBP_ActionRequestItem_Default.uasset
└── Styles/
    ├── DT_DruidsChatStyles.uasset
    └── WS_DruidsChatStyle_Default.uasset
```

### Template Requirements

#### Chat Shell Template (WBP_DruidsChatShell_Default)
- Must inherit from UDruidsSageChatShell
- Must have UListView named "SessionListView"
- Must have UBorder named "ChatViewContainer"
- Must implement "CreateChatViewWidget" event
- Should include session management UI elements

#### Chat View Template (WBP_DruidsChatView_Default)
- Must inherit from UDruidsSageChatView
- Must have UScrollBox named "ChatScrollBox"
- Must have UVerticalBox named "ChatMessageContainer"
- Must have UMultiLineEditableTextBox named "MessageInputBox"
- Must have UButton named "SendButton" and "ClearButton"
- Must have UVerticalBox named "ContextChipContainer"
- Must implement "CreateChatItem" event

#### Chat Item Templates
- Must inherit from UDruidsChatItemBase
- Must implement IDruidsChatItemInterface
- Must have appropriate widget bindings for role, content, and actions
- Should support streaming content updates
- Should handle action button clicks appropriately

## Testing Strategy

### Unit Tests
- Widget creation and destruction
- Blueprint override functionality
- Interface implementation validation
- Memory management verification

### Integration Tests
- Full chat workflow with UMG widgets
- Streaming response handling
- Context switching and updates
- Session management functionality

### Performance Tests
- Widget creation/destruction performance
- Memory usage comparison with Slate
- Rendering performance benchmarks
- Large chat history handling

### User Acceptance Tests
- Designer workflow validation
- Blueprint customization scenarios
- Template creation and modification
- Migration from existing Slate implementation

# Chat System UMG Refactor - Technical Specifications

## Document Information
- **Document Version**: 1.0
- **Created Date**: 2024-12-19
- **Related PRD**: Chat-System-UMG-Refactor-PRD.md
- **Project**: Druids Plugin for Unreal Engine

## Detailed Technical Specifications

### Widget Interface Definitions

#### IDruidsChatItemWidget Interface
```cpp
UINTERFACE(BlueprintType)
class SAGEUI_API UDruidsChatItemWidget : public UInterface
{
    GENERATED_BODY()
};

class SAGEUI_API IDruidsChatItemWidget
{
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
    void SetMessageContent(const FDruidsSageChatMessage& Message);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
    void UpdateFromStreamingContent(const FString& PartialContent);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
    EDruidsSageChatRole GetMessageRole() const;
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Item")
    void OnActionButtonClicked(const FString& ActionData);
};
```

#### Chat Widget Factory Implementation
```cpp
UCLASS(BlueprintType, Blueprintable, Config = Game)
class SAGEUI_API UDruidsChatWidgetFactory : public UObject
{
    GENERATED_BODY()

public:
    // Widget creation methods with Blueprint override support
    UFUNCTION(BlueprintCallable, Category = "Druids Chat Factory")
    UDruidsSageChatShell* CreateChatShell(UObject* Outer = nullptr);
    
    UFUNCTION(BlueprintCallable, Category = "Druids Chat Factory")
    UDruidsSageChatView* CreateChatView(UObject* Outer = nullptr);
    
    UFUNCTION(BlueprintCallable, Category = "Druids Chat Factory")
    UUserWidget* CreateChatItem(EDruidsSageChatRole Role, UObject* Outer = nullptr);
    
    // Blueprint override configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSoftClassPtr<UDruidsSageChatShell> ChatShellOverride;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSoftClassPtr<UDruidsSageChatView> ChatViewOverride;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSoftClassPtr<UUserWidget> AssistantChatItemOverride;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSoftClassPtr<UUserWidget> SimpleChatItemOverride;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget Classes")
    TSoftClassPtr<UUserWidget> ActionRequestItemOverride;

protected:
    // Template discovery and validation
    UFUNCTION(BlueprintCallable, Category = "Template Discovery")
    void DiscoverWidgetTemplates();
    
    UFUNCTION(BlueprintCallable, Category = "Template Validation")
    bool ValidateWidgetTemplate(UClass* WidgetClass, UClass* ExpectedBaseClass);
    
    // Internal creation helpers
    template<typename T>
    T* CreateWidgetFromClass(TSoftClassPtr<T> WidgetClass, UClass* FallbackClass, UObject* Outer);
};
```

### Widget Manager Subsystem
```cpp
UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsChatWidgetManager : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // Subsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    
    UFUNCTION(BlueprintCallable, Category = "Druids Chat", CallInEditor = true)
    static UDruidsChatWidgetManager* Get(const UObject* WorldContext);
    
    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    void SetWidgetFactory(UDruidsChatWidgetFactory* NewFactory);
    
    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    UDruidsChatWidgetFactory* GetWidgetFactory();
    
    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    void RefreshWidgetTemplates();

private:
    UPROPERTY()
    UDruidsChatWidgetFactory* CurrentFactory;
    
    void LoadDefaultFactory();
    void DiscoverProjectTemplates();
};
```

### Configuration System

#### Style Settings Class
```cpp
UCLASS(Config = Game, DefaultConfig, meta = (DisplayName = "Druids Chat Style"))
class SAGEUI_API UDruidsChatStyleSettings : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    UDruidsChatStyleSettings();
    
    // Widget override classes
    UPROPERTY(Config, EditAnywhere, Category = "Widget Overrides", 
        meta = (MetaClass = "/Script/SageUI.DruidsSageChatShell"))
    FSoftClassPath ChatShellClass;
    
    UPROPERTY(Config, EditAnywhere, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/SageUI.DruidsSageChatView"))
    FSoftClassPath ChatViewClass;
    
    UPROPERTY(Config, EditAnywhere, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/UMG.UserWidget"))
    FSoftClassPath AssistantChatItemClass;
    
    UPROPERTY(Config, EditAnywhere, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/UMG.UserWidget"))
    FSoftClassPath SimpleChatItemClass;
    
    UPROPERTY(Config, EditAnywhere, Category = "Widget Overrides",
        meta = (MetaClass = "/Script/UMG.UserWidget"))
    FSoftClassPath ActionRequestItemClass;
    
    // Style configuration
    UPROPERTY(Config, EditAnywhere, Category = "Appearance")
    FLinearColor PrimaryColor = FLinearColor(0.2f, 0.4f, 0.8f);
    
    UPROPERTY(Config, EditAnywhere, Category = "Appearance")
    FLinearColor SecondaryColor = FLinearColor(0.3f, 0.3f, 0.3f);
    
    UPROPERTY(Config, EditAnywhere, Category = "Appearance")
    FLinearColor AccentColor = FLinearColor(0.1f, 0.8f, 0.3f);
    
    // Auto-discovery settings
    UPROPERTY(Config, EditAnywhere, Category = "Template Discovery")
    bool bAutoDiscoverTemplates = true;
    
    UPROPERTY(Config, EditAnywhere, Category = "Template Discovery")
    TArray<FString> TemplateSearchPaths = {
        "/Game/Druids/UI/Templates/",
        "/Game/UI/Druids/",
        "/Game/Widgets/Chat/"
    };
    
    UPROPERTY(Config, EditAnywhere, Category = "Template Discovery")
    FString TemplatePrefix = "WBP_Druids";
    
    // Performance settings
    UPROPERTY(Config, EditAnywhere, Category = "Performance")
    int32 MaxChatHistoryItems = 1000;
    
    UPROPERTY(Config, EditAnywhere, Category = "Performance")
    bool bEnableWidgetPooling = true;
    
    UPROPERTY(Config, EditAnywhere, Category = "Performance")
    int32 WidgetPoolSize = 50;

#if WITH_EDITOR
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif
};
```

### Base Widget Classes

#### UDruidsSageChatShell Base Class
```cpp
UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageChatShell : public UUserWidget
{
    GENERATED_BODY()

public:
    UDruidsSageChatShell(const FObjectInitializer& ObjectInitializer);
    
    // Blueprint events for customization
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell")
    void OnChatSessionChanged(const FString& SessionName);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell")
    void OnMessageSending();
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell")
    void OnChatViewCreated(UDruidsSageChatView* ChatView);
    
    // Public interface
    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    void SetChatRequestHandler(TScriptInterface<IChatRequestHandler> Handler);
    
    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    void SetExtensionDelegator(TScriptInterface<ISageExtensionDelegator> Delegator);
    
    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    UDruidsSageChatView* GetCurrentChatView() const;
    
    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    void CreateNewChatSession(const FString& SessionName);

protected:
    // Widget references (can be bound in Blueprint)
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UListView* SessionListView;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UPanelWidget* ChatViewContainer;
    
    UPROPERTY(BlueprintReadOnly, Category = "Chat Shell")
    UDruidsSageChatView* CurrentChatView;
    
    // Internal state
    UPROPERTY()
    TScriptInterface<IChatRequestHandler> ChatRequestHandler;
    
    UPROPERTY()
    TScriptInterface<ISageExtensionDelegator> ExtensionDelegator;
    
    UPROPERTY()
    TArray<FString> ChatSessions;
    
    // Blueprint implementable functions
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell")
    UDruidsSageChatView* CreateChatView();
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat Shell")
    void InitializeChatSessions();
    
    // Native implementations with Blueprint override support
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;
    
    UFUNCTION(BlueprintCallable, Category = "Chat Shell")
    void InitializeChatSession(const FString& SessionName);
};
```

#### UDruidsSageChatView Base Class
```cpp
UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageChatView : public UUserWidget
{
    GENERATED_BODY()

public:
    UDruidsSageChatView(const FObjectInitializer& ObjectInitializer);
    
    // Blueprint events
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View")
    void OnMessageReceived(const FDruidsSageChatMessage& Message);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View")
    void OnContextChanged(const FString& Context, const FString& DisplayMessage);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View")
    void OnStreamingUpdate(const FString& PartialContent);
    
    // Public interface
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SendMessage(const FString& MessageText, EDruidsSageChatRole Role = EDruidsSageChatRole::User);
    
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void ClearChat();
    
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SetTabContext(const FString& Context, const FString& DisplayMessage);
    
    UFUNCTION(BlueprintCallable, Category = "Chat View")
    void SetBlueprintContext(const FString& Context, const FString& DisplayMessage);

protected:
    // Widget references (can be bound in Blueprint)
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UScrollBox* ChatScrollBox;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UVerticalBox* ChatMessageContainer;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UMultiLineEditableTextBox* MessageInputBox;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UButton* SendButton;
    
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UButton* ClearButton;
    
    // Internal state
    UPROPERTY(BlueprintReadOnly, Category = "Chat View")
    FString SessionID;
    
    UPROPERTY(BlueprintReadOnly, Category = "Chat View")
    TArray<UUserWidget*> ChatItems;
    
    UPROPERTY()
    TScriptInterface<IChatRequestHandler> ChatRequestHandler;
    
    UPROPERTY()
    TScriptInterface<ISageExtensionDelegator> ExtensionDelegator;
    
    // Blueprint implementable functions
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View")
    UUserWidget* CreateChatItem(EDruidsSageChatRole Role, const FDruidsSageChatMessage& Message);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Chat View")
    void OnChatItemCreated(UUserWidget* ChatItem);
    
    // Native implementations
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;
    
    UFUNCTION()
    void OnSendButtonClicked();
    
    UFUNCTION()
    void OnClearButtonClicked();
    
    UFUNCTION()
    void OnMessageInputChanged(const FText& Text);
};
```

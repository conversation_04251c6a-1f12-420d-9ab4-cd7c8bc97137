# Product Requirements Document: Action Request Chat Item State Management

## Document Information
- **Document Version**: 1.0
- **Created Date**: 2025-01-02
- **Project**: Druids Plugin for Unreal Engine
- **Feature**: Action Request Chat Item State Management

## Executive Summary

This PRD outlines the enhancement of the Action Request Chat Item UI component to provide clear state feedback for both extension actions and invocation actions. The current implementation lacks proper state management and user feedback during action execution, leading to poor user experience. This enhancement will implement a comprehensive state-based UX flow with proper error handling and retry mechanisms.

## Problem Statement

### Current State
The Action Request Chat Item (`UDruidsSageActionRequestChatItem`) currently:
- Shows an "Action" button before execution
- Disables the button after clicking without clear state indication
- Lacks differentiation between extension actions and invocation actions
- Has minimal error handling and no retry mechanism
- Provides poor user feedback during action execution

### Issues with Current Implementation
1. **Poor State Feedback**: Users cannot tell if an action is processing, succeeded, or failed
2. **No Retry Mechanism**: Failed actions cannot be retried by users
3. **Inconsistent Error Handling**: Extension actions and invocation actions handle errors differently
4. **Limited User Control**: No way to distinguish between different action types or their capabilities
5. **Unclear Progress**: No indication of what's happening during action execution

## Goals and Objectives

### Primary Goals
1. **Clear State Management**: Implement distinct button states for different action phases
2. **Action Type Differentiation**: Handle extension actions and invocation actions with appropriate UX flows
3. **Error Handling**: Provide comprehensive error handling with user-friendly messages
4. **Retry Mechanism**: Allow users to retry failed invocation actions with limits
5. **Progress Indication**: Show clear feedback during action execution

### Secondary Goals
1. **Consistent UX**: Ensure consistent behavior across all action types
2. **User Education**: Help users understand what actions are doing
3. **Error Recovery**: Provide pathways for users to recover from failures
4. **Performance Feedback**: Show execution results and response messages

## Target Users

### Primary Users
- **Game Developers**: Need reliable action execution with clear feedback
- **Technical Artists**: Require understanding of action success/failure states
- **QA Teams**: Need clear indication of action results for testing

### Secondary Users
- **Project Managers**: Benefit from clear action completion status
- **Support Teams**: Need clear error messages for troubleshooting

## Functional Requirements

### FR-1: Extension Action State Management
**Priority**: High
**Description**: Implement state management for extension actions (actions with "extension_id")

#### FR-1.1: Button States
- **Initial State**: Button displays "Action"
- **Working State**: Button displays "Working" when clicked
- **Success State**: Button displays "Done" and is disabled (grayed out)
- **Error State**: Button displays "Error" and is disabled (grayed out)

#### FR-1.2: Error Handling
- Display error messages when extension actions fail
- Support error reporting mechanism for Blueprint-based extensions
- Show generic error message for code-level failures

#### FR-1.3: Extension Error Reporting
- Implement utility function for Blueprint extensions to report errors
- Register error reporting mechanism with extension system
- Return structured error information to UI

### FR-2: Invocation Action State Management
**Priority**: High
**Description**: Implement state management for invocation actions (actions with "prompt")

#### FR-2.1: Button States
- **Initial State**: Button displays "Action"
- **Working State**: Button displays "Working" when clicked
- **Success State**: Button displays "Done" and is disabled (grayed out)
- **Try Again State**: Button displays "Try Again" after first failure
- **Failed State**: Button displays "Failed" and is disabled after second failure

#### FR-2.2: Execution Tracking
- Track execution attempts (maximum 2 attempts)
- Show execution status for each attempt
- Display "executing..." during each attempt
- Show "success" or "failure" result for each attempt

#### FR-2.3: Response Handling
- Display response messages from successful invocations
- Show generic error messages for failed invocations
- Clear previous execution lines on retry

### FR-3: State Transition Logic
**Priority**: High
**Description**: Implement proper state transitions for both action types

#### FR-3.1: Extension Action Flow
```
[Action] → [Working] → [Done] (success)
                   → [Error] (failure)
```

#### FR-3.2: Invocation Action Flow
```
[Action] → [Working] → [Done] (success)
                   → [Try Again] (failure, attempt 1)
                   
[Try Again] → [Working] → [Done] (success)
                      → [Failed] (failure, attempt 2)
```

### FR-4: Error Message Display
**Priority**: Medium
**Description**: Implement comprehensive error message display

#### FR-4.1: Error Message Widget
- Show/hide error message widget based on action results
- Display appropriate error messages for different failure types
- Support both extension-specific and generic error messages

#### FR-4.2: Error Message Content
- Extension errors: Custom error messages from Blueprint functions
- Invocation errors: Generic "Action failed" messages
- Code errors: Technical error messages for debugging

### FR-5: Response Message Display
**Priority**: Medium
**Description**: Display response messages from successful invocation actions

#### FR-5.1: Response Widget
- Show response messages from successful invocation requests
- Hide response widget when not applicable
- Format response messages appropriately

## Technical Requirements

### TR-1: Action Type Detection
**Priority**: High
**Description**: Detect action type from JSON content

#### TR-1.1: Extension Action Detection
- Check for "extension_id" field in action JSON
- Route to extension delegator for processing

#### TR-1.2: Invocation Action Detection
- Check for "prompt" field in action JSON
- Route to code executor for invocation processing

### TR-2: State Management Implementation
**Priority**: High
**Description**: Implement state tracking and UI updates

#### TR-2.1: State Enumeration
```cpp
UENUM(BlueprintType)
enum class EActionRequestState : uint8
{
    Ready,      // Initial state - "Action" button
    Working,    // Processing - "Working" button
    Done,       // Success - "Done" button (disabled)
    Error,      // Extension error - "Error" button (disabled)
    TryAgain,   // Invocation failure - "Try Again" button
    Failed      // Final failure - "Failed" button (disabled)
};
```

#### TR-2.2: Attempt Tracking
- Track number of execution attempts for invocation actions
- Maximum 2 attempts for invocation actions
- Reset attempt count on successful execution

### TR-3: Extension Error Reporting
**Priority**: High
**Description**: Implement error reporting mechanism for extensions

#### TR-3.1: Blueprint Utility Function
- Create Blueprint-callable function for error reporting
- Register with extension system for error collection
- Return error information to action result handler

#### TR-3.2: Error Collection
- Collect errors from Blueprint extension functions
- Pass error information through FSageActionResult
- Display errors in action request chat item

### TR-4: UI Widget Updates
**Priority**: High
**Description**: Update UI widgets based on state changes

#### TR-4.1: Button Text Updates
- Update button text based on current state
- Handle button enable/disable states
- Maintain consistent styling across states

#### TR-4.2: Widget Visibility
- Show/hide error message widgets
- Show/hide response message widgets
- Manage execution status display

## User Experience Requirements

### UX-1: Visual Feedback
**Priority**: High
**Description**: Provide clear visual feedback for all states

#### UX-1.1: Button States
- Distinct button text for each state
- Appropriate button styling (enabled/disabled)
- Consistent visual language across action types

#### UX-1.2: Progress Indication
- Clear "Working" state during execution
- Execution attempt indicators for invocation actions
- Success/failure indicators for each attempt

### UX-2: Error Communication
**Priority**: High
**Description**: Communicate errors clearly to users

#### UX-2.1: Error Messages
- User-friendly error messages
- Actionable error information when possible
- Clear distinction between different error types

#### UX-2.2: Recovery Options
- Retry mechanism for invocation actions
- Clear indication when no retry is available
- Guidance on next steps after failures

### UX-3: Response Display
**Priority**: Medium
**Description**: Display action responses appropriately

#### UX-3.1: Success Messages
- Show response messages from successful invocations
- Format messages for readability
- Distinguish between action completion and response content

## Implementation Plan

### Phase 1: Core State Management (2 weeks)
1. **Week 1**: Implement state enumeration and basic state transitions
2. **Week 2**: Update UI widgets and button text based on states

### Phase 2: Action Type Handling (2 weeks)
1. **Week 1**: Implement extension action state flow
2. **Week 2**: Implement invocation action state flow with retry logic

### Phase 3: Error Handling and Polish (1 week)
1. **Week 1**: Implement error reporting, response display, and final testing

## Success Criteria

### Functional Success Criteria
- [ ] Extension actions show proper state transitions (Action → Working → Done/Error)
- [ ] Invocation actions support retry mechanism with proper state flow
- [ ] Error messages display appropriately for all failure types
- [ ] Response messages show for successful invocation actions
- [ ] Button states update correctly for all scenarios

### User Experience Success Criteria
- [ ] Users can clearly understand action execution status
- [ ] Failed invocation actions can be retried up to 2 times
- [ ] Error messages provide meaningful feedback
- [ ] UI remains responsive and informative throughout action execution

## Risk Assessment

### Technical Risks
- **State synchronization**: Risk of UI state getting out of sync with action execution
- **Error handling complexity**: Different error types may require different handling approaches
- **Async action management**: Proper handling of async invocation actions

### Mitigation Strategies
- Implement comprehensive state validation
- Create unified error handling interface
- Use proper async result handlers for invocation actions

## Dependencies

### Internal Dependencies
- FSageActionResult system for action result communication
- Extension delegator system for extension action execution
- Code executor system for invocation action execution
- ISageActionResultHandler for async result handling

### External Dependencies
- UMG widget system for UI updates
- Blueprint system for extension error reporting
- HTTP request system for invocation actions

## Acceptance Criteria

1. Extension actions transition through states: Action → Working → Done/Error
2. Invocation actions support retry: Action → Working → Done/Try Again → Working → Done/Failed
3. Error messages display for failed actions
4. Response messages display for successful invocation actions
5. Button text updates correctly for all states
6. UI remains consistent across different action types
7. Retry mechanism works correctly with attempt limits
8. Error reporting works for Blueprint-based extensions

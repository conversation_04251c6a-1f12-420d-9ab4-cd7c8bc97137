# Action Request Chat Item State Management - Implementation Tasks

## Document Information
- **Document Version**: 1.0
- **Created Date**: 2025-01-02
- **Related PRD**: Action-Request-Chat-Item-State-Management-PRD.md
- **Project**: Druids Plugin for Unreal Engine

## Implementation Overview

This document provides a detailed task breakdown for implementing the Action Request Chat Item State Management feature as specified in the PRD. Tasks are organized by implementation phases and include specific code changes, file modifications, and testing requirements.

## Phase 1: Core State Management (Week 1-2)

### Task 1.1: Define State Enumeration
**Estimated Time**: 2 hours
**Files**: `DruidsSageActionRequestChatItem.h`

- [ ] Add `EActionRequestState` enum to header file:
```cpp
UENUM(BlueprintType)
enum class EActionRequestState : uint8
{
    Ready,      // Initial state - "Action" button
    Working,    // Processing - "Working" button  
    Done,       // Success - "Done" button (disabled)
    Error,      // Extension error - "Error" button (disabled)
    TryAgain,   // Invocation failure - "Try Again" button
    Failed      // Final failure - "Failed" button (disabled)
};
```
- [ ] Add state tracking member variable: `EActionRequestState CurrentState`
- [ ] Add attempt tracking member variable: `int32 ExecutionAttempts`
- [ ] Add action type tracking member variable: `bool bIsInvocationAction`

### Task 1.2: Implement State Transition Functions
**Estimated Time**: 4 hours
**Files**: `DruidsSageActionRequestChatItem.h`, `DruidsSageActionRequestChatItem.cpp`

- [ ] Add `SetActionState(EActionRequestState NewState)` function
- [ ] Add `GetButtonTextForState(EActionRequestState State)` function
- [ ] Add `IsButtonEnabledForState(EActionRequestState State)` function
- [ ] Add `ResetActionState()` function for initialization
- [ ] Add `CanRetryAction()` function to check retry eligibility

### Task 1.3: Update Button Text and State Logic
**Estimated Time**: 3 hours
**Files**: `DruidsSageActionRequestChatItem.cpp`

- [ ] Implement `GetButtonTextForState()` with proper text mapping:
  - Ready: "Action"
  - Working: "Working"
  - Done: "Done"
  - Error: "Error"
  - TryAgain: "Try Again"
  - Failed: "Failed"
- [ ] Implement `IsButtonEnabledForState()` logic
- [ ] Update `SetupWidgets()` to initialize button state
- [ ] Add button text update in `SetActionState()`

### Task 1.4: Modify Button Click Handler
**Estimated Time**: 2 hours
**Files**: `DruidsSageActionRequestChatItem.cpp`

- [ ] Update `OnApplyButtonClicked()` to check current state
- [ ] Handle retry logic for TryAgain state
- [ ] Prevent clicks in disabled states
- [ ] Set Working state when action starts
- [ ] Track execution attempts for invocation actions

## Phase 2: Action Type Detection and Handling (Week 3-4)

### Task 2.1: Implement Action Type Detection
**Estimated Time**: 3 hours
**Files**: `DruidsSageActionRequestChatItem.cpp`

- [ ] Add `DetermineActionType()` function in `UpdateFromContentJson()`
- [ ] Check for "extension_id" field to identify extension actions
- [ ] Check for "prompt" field to identify invocation actions
- [ ] Set `bIsInvocationAction` flag based on detection
- [ ] Add logging for action type detection

### Task 2.2: Update HandleActionResult for State Management
**Estimated Time**: 4 hours
**Files**: `DruidsSageActionRequestChatItem.cpp`

- [ ] Modify `HandleActionResult()` to use new state system
- [ ] Implement extension action result handling:
  - Success: Set Done state
  - Failure: Set Error state
- [ ] Implement invocation action result handling:
  - Success: Set Done state
  - First failure: Set TryAgain state
  - Second failure: Set Failed state
- [ ] Update execution attempt tracking
- [ ] Remove old button disable logic

### Task 2.3: Add Response Message Display
**Estimated Time**: 3 hours
**Files**: `DruidsSageActionRequestChatItem.h`, `DruidsSageActionRequestChatItem.cpp`

- [ ] Add response message widget member variable
- [ ] Add `ShowResponseMessage(const FString& Message)` function
- [ ] Add `HideResponseMessage()` function
- [ ] Update `HandleActionResult()` to show response for successful invocations
- [ ] Add response message widget to Blueprint if needed

### Task 2.4: Enhance Error Message Display
**Estimated Time**: 2 hours
**Files**: `DruidsSageActionRequestChatItem.cpp`

- [ ] Update error message display logic in `HandleActionResult()`
- [ ] Show error messages for Error state
- [ ] Hide error messages for success states
- [ ] Add different error message handling for extension vs invocation actions

## Phase 3: Extension Error Reporting System (Week 5)

### Task 3.1: Create Extension Error Reporting Interface
**Estimated Time**: 4 hours
**Files**: New files in `SageExtensions` module

- [ ] Create `ISageExtensionErrorReporter.h` interface
- [ ] Add `ReportExtensionError(const FString& ErrorMessage)` function
- [ ] Create `SageExtensionErrorReporter.h/.cpp` implementation
- [ ] Add error collection and storage mechanism

### Task 3.2: Create Blueprint Utility Function
**Estimated Time**: 3 hours
**Files**: `SageExtension.h`, `SageExtension.cpp`

- [ ] Add `UFUNCTION(BlueprintCallable)` for error reporting:
```cpp
UFUNCTION(BlueprintCallable, Category = "Sage Extension")
static void ReportExtensionError(const FString& ErrorMessage);
```
- [ ] Implement function to store error in thread-local or global storage
- [ ] Add error retrieval mechanism for action execution

### Task 3.3: Integrate Error Reporting with Extension Execution
**Estimated Time**: 3 hours
**Files**: `SageExtensionDelegator.cpp`, `SageExtension.cpp`

- [ ] Modify `FSageExtensionDelegator::OnActionApplied()` to check for reported errors
- [ ] Update `USageExtension::ExecuteAction()` to clear previous errors
- [ ] Return `FSageActionResult::ExtensionFailure()` with collected error messages
- [ ] Add error message to result when Blueprint reports error

### Task 3.4: Update FSageActionResult for Better Error Handling
**Estimated Time**: 2 hours
**Files**: `SageActionTypes.h`

- [ ] Ensure `FSageActionResult` properly carries error messages
- [ ] Add any missing constructors for error scenarios
- [ ] Update extension success/failure factory methods if needed

## Phase 4: UI Widget Integration (Week 6)

### Task 4.1: Add Execution Status Display
**Estimated Time**: 3 hours
**Files**: `DruidsSageActionRequestChatItem.h`, `DruidsSageActionRequestChatItem.cpp`

- [ ] Add execution status widget member variables
- [ ] Add `ShowExecutionStatus(const FString& Status)` function
- [ ] Add `ClearExecutionStatus()` function for retries
- [ ] Display "executing..." during Working state
- [ ] Display "success"/"failure" after each attempt

### Task 4.2: Update Widget Visibility Management
**Estimated Time**: 2 hours
**Files**: `DruidsSageActionRequestChatItem.cpp`

- [ ] Update `SetActionState()` to manage widget visibility
- [ ] Show/hide error messages based on state
- [ ] Show/hide response messages based on action type and result
- [ ] Show/hide execution status based on state

### Task 4.3: Add State-Based Styling
**Estimated Time**: 2 hours
**Files**: `DruidsSageActionRequestChatItem.cpp`

- [ ] Add button styling updates in `SetActionState()`
- [ ] Apply disabled styling for Done, Error, Failed states
- [ ] Apply enabled styling for Ready, TryAgain states
- [ ] Add working/processing styling for Working state

## Phase 5: Integration and Testing (Week 7)

### Task 5.1: Update Chat View Integration
**Estimated Time**: 2 hours
**Files**: `DruidsSageChatView.cpp`

- [ ] Verify `OnActionRequestApplied()` works with new state system
- [ ] Ensure async action handling works with state management
- [ ] Test extension and invocation action flows
- [ ] Verify error reporting integration

### Task 5.2: Update Assistant Chat Item Integration
**Estimated Time**: 1 hour
**Files**: `DruidsSageAssistantChatItem.cpp`

- [ ] Verify `HandleActionResult()` passes correct parameters
- [ ] Test action request chat item creation and initialization
- [ ] Ensure proper cleanup of action request references

### Task 5.3: Add Comprehensive Logging
**Estimated Time**: 2 hours
**Files**: All modified files

- [ ] Add state transition logging
- [ ] Add action type detection logging
- [ ] Add execution attempt logging
- [ ] Add error reporting logging
- [ ] Use appropriate log categories (LogDruidsSage_Internal, etc.)

### Task 5.4: Create Unit Tests
**Estimated Time**: 4 hours
**Files**: New test files

- [ ] Create test for state transitions
- [ ] Create test for action type detection
- [ ] Create test for retry logic
- [ ] Create test for error reporting mechanism
- [ ] Create test for button text and state updates

## Phase 6: Documentation and Polish (Week 8)

### Task 6.1: Update Blueprint Documentation
**Estimated Time**: 2 hours
**Files**: Documentation files

- [ ] Document new error reporting function for Blueprint extensions
- [ ] Create examples of proper error reporting usage
- [ ] Update extension development guidelines

### Task 6.2: Add Code Comments and Documentation
**Estimated Time**: 2 hours
**Files**: All modified files

- [ ] Add comprehensive header comments for new functions
- [ ] Document state transition logic
- [ ] Document action type detection logic
- [ ] Add inline comments for complex logic

### Task 6.3: Performance Testing and Optimization
**Estimated Time**: 2 hours

- [ ] Test action execution performance with new state system
- [ ] Verify no memory leaks in state management
- [ ] Test UI responsiveness during state transitions
- [ ] Optimize any performance bottlenecks

### Task 6.4: User Acceptance Testing
**Estimated Time**: 3 hours

- [ ] Test extension action flow: Action → Working → Done/Error
- [ ] Test invocation action flow: Action → Working → Done/Try Again → Working → Done/Failed
- [ ] Test error message display for various failure scenarios
- [ ] Test response message display for successful invocations
- [ ] Test retry mechanism with proper attempt limits
- [ ] Verify button states and text updates correctly

## Risk Mitigation Tasks

### Task R.1: State Synchronization Validation
**Estimated Time**: 2 hours

- [ ] Add state validation functions
- [ ] Implement state consistency checks
- [ ] Add recovery mechanisms for invalid states
- [ ] Test concurrent action execution scenarios

### Task R.2: Error Handling Edge Cases
**Estimated Time**: 2 hours

- [ ] Test error reporting with malformed JSON
- [ ] Test error reporting with missing action fields
- [ ] Test error reporting with network failures
- [ ] Add fallback error messages for unknown scenarios

### Task R.3: Async Action Management
**Estimated Time**: 2 hours

- [ ] Test async action cancellation scenarios
- [ ] Test multiple concurrent action requests
- [ ] Verify proper cleanup of async handlers
- [ ] Test memory management for long-running actions

## Acceptance Criteria Verification

### Final Verification Tasks
- [ ] Extension actions transition: Action → Working → Done/Error ✓
- [ ] Invocation actions support retry: Action → Working → Done/Try Again → Working → Done/Failed ✓
- [ ] Error messages display for failed actions ✓
- [ ] Response messages display for successful invocation actions ✓
- [ ] Button text updates correctly for all states ✓
- [ ] UI remains consistent across different action types ✓
- [ ] Retry mechanism works with attempt limits ✓
- [ ] Error reporting works for Blueprint-based extensions ✓

## Total Estimated Time: 6-8 weeks
## Total Tasks: 35 implementation tasks + 8 verification tasks

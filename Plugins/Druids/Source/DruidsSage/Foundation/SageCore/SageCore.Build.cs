using UnrealBuildTool;

public class SageCore : ModuleRules
{
    public SageCore(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "UMG",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "Engine",
                "Slate",
                "SlateCore",
                "Json",
                "Projects",
                "RHI", 
            }
        );
        
        // Add editor-specific dependencies only when building for editor
        if (Target.bBuildEditor == true)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "UnrealEd",
                    "PluginBrowser",
                }
            );
        }

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "DruidsCore",

                "SageCommonTypes",
                
                "SageJSONUtilities",
            }
        );
    }
}
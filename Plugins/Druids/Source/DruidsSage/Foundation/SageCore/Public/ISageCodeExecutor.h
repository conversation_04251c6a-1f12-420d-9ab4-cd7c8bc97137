#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "SageActionTypes.h"

// Forward declarations
class ISageInvocationRequestFactory;
class ISageActionResultHandler;

class SAGECORE_API ISageCodeExecutor
{
public:
	virtual ~ISageCodeExecutor() = default;

	virtual FSageActionResult OnActionApplied(const TSharedPtr<FJsonValue>& ActionDetails) = 0;

	/**
	 * Set the invocation request factory for creating HTTP requests
	 * @param Factory The factory to use for creating invocation requests
	 */
	virtual void SetInvocationRequestFactory(TSharedPtr<ISageInvocationRequestFactory> Factory) = 0;

	/**
	 * Set the action result handler for async results
	 * @param Handler The handler to use for async action results
	 */
	virtual void SetActionResultHandler(TSharedPtr<ISageActionResultHandler> Handler) = 0;
};

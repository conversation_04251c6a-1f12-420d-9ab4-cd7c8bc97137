#pragma once

#include "CoreMinimal.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"

// Forward declarations
class ISageInvocationRequest;

/**
 * Interface for creating invocation requests
 * This allows SagePython to create invocation requests without depending on SageNetworking
 */
class ISageInvocationRequestFactory
{
public:
	virtual ~ISageInvocationRequestFactory() {}

	/**
	 * Create an invocation request for the given prompt
	 * @param Prompt The prompt string to send
	 * @return Shared pointer to the created invocation request, or nullptr if creation failed
	 */
	virtual TSharedPtr<ISageInvocationRequest> CreateInvocationRequest(const FString& Prompt) = 0;
};

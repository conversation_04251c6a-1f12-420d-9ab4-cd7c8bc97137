#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphNode.h"
#include "EdGraph/EdGraphPin.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "BlueprintNodeModifier.generated.h"

/**
 * Utility class for modifying Blueprint node graphs at Editor time using JSON input
 */
UCLASS()
class SAGEBLUEPRINTUTILITIES_API UBlueprintNodeModifier : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    /**
     * Modifies Blueprint nodes based on JSON input
     * @param JsonInput - JSON string containing actions to perform
     * @param OutErrorMessage - Error message if the operation fails
     * @return True if all actions were successfully applied
     */
    UFUNCTION(BlueprintCallable, Category = "Druids|Blueprint|Utilities", meta = (DisplayName = "Modify Blueprint Nodes"))
    static bool ModifyBlueprintNodes(const FString& JsonInput, FString& OutErrorMessage);

private:
    // Action types
    static const FString ACTION_REMOVE_CONNECTION;
    static const FString ACTION_DELETE_NODE;
    static const FString ACTION_INSERT_NODE;
    static const FString ACTION_ADD_CONNECTION;

    // Pin types
    static const FString PIN_TYPE_EXEC;
    static const FString PIN_TYPE_BOOL;
    static const FString PIN_TYPE_FLOAT;
    static const FString PIN_TYPE_REAL;
    static const FString PIN_TYPE_STRUCT;
    static const FString PIN_TYPE_OBJECT;

    // Helper struct to store temporary node IDs and their corresponding actual nodes
    struct FTempNodeMapping
    {
        FString TempId;
        UEdGraphNode* Node;
    };

    // Helper functions
    static bool ValidateJsonSchema(const TSharedPtr<FJsonObject>& JsonObject, FString& OutErrorMessage);
    static bool ProcessActions(const TArray<TSharedPtr<FJsonValue>>& Actions, FString& OutErrorMessage);
    
    // Action handlers
    static bool HandleRemoveConnection(const TSharedPtr<FJsonObject>& ActionObject, TArray<FTempNodeMapping>& TempNodes, FString& OutErrorMessage);
    static bool HandleDeleteNode(const TSharedPtr<FJsonObject>& ActionObject, FString& OutErrorMessage);
    static bool HandleInsertNode(const TSharedPtr<FJsonObject>& ActionObject, TArray<FTempNodeMapping>& TempNodes, FString& OutErrorMessage);
    static bool HandleAddConnection(const TSharedPtr<FJsonObject>& ActionObject, TArray<FTempNodeMapping>& TempNodes, FString& OutErrorMessage);
    
    // Utility functions
    static UEdGraphNode* FindNodeById(const FString& NodeId, const TArray<FTempNodeMapping>& TempNodes);
    static UEdGraphPin* FindPinByName(UEdGraphNode* Node, const FString& PinName, EEdGraphPinDirection Direction);
    static bool IsValidNodeId(const FString& NodeId);
    static bool IsValidTempNodeId(const FString& TempId);
    static FEdGraphPinType GetPinTypeFromString(const FString& PinTypeStr);
};

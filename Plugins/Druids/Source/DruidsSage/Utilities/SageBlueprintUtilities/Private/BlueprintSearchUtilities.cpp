#include "BlueprintSearchUtilities.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Engine/Blueprint.h"
#include "LogDruids.h"

TArray<FBlueprintSearchResultItem> UBlueprintSearchUtilities::FindBlueprintsByName(const FString& SearchQuery)
{
    TArray<FBlueprintSearchResultItem> Results;

    if (SearchQuery.IsEmpty())
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("Empty search query provided for Blueprint search."));
        return Results;
    }

    // Parse search query into individual terms
    TArray<FString> SearchTerms;
    SearchQuery.ParseIntoArray(SearchTerms, TEXT(" "), true);

    if (SearchTerms.Num() == 0)
    {
        return Results;
    }

    // Get the asset registry
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // Wait for asset discovery to complete if still loading
    if (AssetRegistry.IsLoadingAssets())
    {
        AssetRegistry.WaitForCompletion();
    }

    // Get all Blueprint assets
    TArray<FAssetData> BlueprintAssets;
    FARFilter Filter;
    Filter.ClassPaths.Add(UBlueprint::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;
    AssetRegistry.GetAssets(Filter, BlueprintAssets);

    // Score each Blueprint and collect results
    TArray<FBlueprintSearchResultItem> AllResults;
    for (const FAssetData& AssetData : BlueprintAssets)
    {
        FString BlueprintName = AssetData.AssetName.ToString();
        float Score = CalculateRelevanceScore(BlueprintName, SearchTerms);

        if (Score > 0.0f)
        {
            // Load the Blueprint class
            UBlueprint* Blueprint = Cast<UBlueprint>(AssetData.GetAsset());
            if (Blueprint && Blueprint->GeneratedClass)
            {
                AllResults.Add(FBlueprintSearchResultItem(Blueprint->GeneratedClass, Score));
            }
        }
    }

    // Sort by relevance score (highest first)
    AllResults.Sort([](const FBlueprintSearchResultItem& A, const FBlueprintSearchResultItem& B)
    {
        return A.RelevanceScore > B.RelevanceScore;
    });

    // Return top 10 results
    int32 MaxResults = FMath::Min(10, AllResults.Num());
    for (int32 i = 0; i < MaxResults; i++)
    {
        Results.Add(AllResults[i]);
    }

    return Results;
}

float UBlueprintSearchUtilities::CalculateRelevanceScore(const FString& BlueprintName, const TArray<FString>& SearchTerms)
{
    if (BlueprintName.IsEmpty() || SearchTerms.Num() == 0)
    {
        return 0.0f;
    }

    FString LowerBlueprintName = BlueprintName.ToLower();
    TArray<FString> LowerSearchTerms;
    for (const FString& Term : SearchTerms)
    {
        LowerSearchTerms.Add(Term.ToLower());
    }

    float Score = 0.0f;

    // Check for exact match in order (highest score)
    if (IsExactMatch(LowerBlueprintName, LowerSearchTerms))
    {
        Score = 100.0f;
    }
    // Check if all terms are present (any order)
    else if (ContainsAllTerms(LowerBlueprintName, LowerSearchTerms))
    {
        Score = 80.0f;
    }
    // Partial match based on number of terms found
    else
    {
        int32 MatchingTerms = CountMatchingTerms(LowerBlueprintName, LowerSearchTerms);
        if (MatchingTerms > 0)
        {
            float MatchRatio = static_cast<float>(MatchingTerms) / static_cast<float>(LowerSearchTerms.Num());
            Score = 60.0f * MatchRatio;
        }
    }

    // Apply length penalty - reduce score for longer names
    if (Score > 0.0f)
    {
        int32 SearchTermsLength = 0;
        for (const FString& Term : LowerSearchTerms)
        {
            SearchTermsLength += Term.Len();
        }

        int32 ExtraLength = FMath::Max(0, LowerBlueprintName.Len() - SearchTermsLength);
        float LengthPenalty = FMath::Min(0.5f, ExtraLength * 0.01f); // Max 50% penalty
        Score *= (1.0f - LengthPenalty);
    }

    return Score;
}

bool UBlueprintSearchUtilities::IsExactMatch(const FString& BlueprintName, const TArray<FString>& SearchTerms)
{
    if (SearchTerms.Num() == 0)
    {
        return false;
    }

    // Build the search string by joining terms with spaces
    FString SearchString;
    for (int32 i = 0; i < SearchTerms.Num(); i++)
    {
        if (i > 0)
        {
            SearchString += TEXT(" ");
        }
        SearchString += SearchTerms[i];
    }

    return BlueprintName.Contains(SearchString);
}

bool UBlueprintSearchUtilities::ContainsAllTerms(const FString& BlueprintName, const TArray<FString>& SearchTerms)
{
    for (const FString& Term : SearchTerms)
    {
        if (!BlueprintName.Contains(Term))
        {
            return false;
        }
    }
    return true;
}

int32 UBlueprintSearchUtilities::CountMatchingTerms(const FString& BlueprintName, const TArray<FString>& SearchTerms)
{
    int32 Count = 0;
    for (const FString& Term : SearchTerms)
    {
        if (BlueprintName.Contains(Term))
        {
            Count++;
        }
    }
    return Count;
}

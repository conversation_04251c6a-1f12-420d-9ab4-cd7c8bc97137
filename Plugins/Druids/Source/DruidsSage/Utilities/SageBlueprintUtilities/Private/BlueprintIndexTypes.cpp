#include "BlueprintIndexTypes.h"

FString FBlueprintIndexTypes::NodeTypeToString(EBlueprintNodeType NodeType)
{
    switch (NodeType)
    {
    case EBlueprintNodeType::Function:
        return TEXT("Function");
    case EBlueprintNodeType::Event:
        return TEXT("Event");
    case EBlueprintNodeType::Variable:
        return TEXT("Variable");
    case EBlueprintNodeType::Component:
        return TEXT("Component");
    case EBlueprintNodeType::Other:
    default:
        return TEXT("Other");
    }
}

EBlueprintNodeType FBlueprintIndexTypes::StringToNodeType(const FString& NodeTypeString)
{
    if (NodeTypeString == TEXT("Function"))
    {
        return EBlueprintNodeType::Function;
    }
    else if (NodeTypeString == TEXT("Event"))
    {
        return EBlueprintNodeType::Event;
    }
    else if (NodeTypeString == TEXT("Variable"))
    {
        return EBlueprintNodeType::Variable;
    }
    else if (NodeTypeString == TEXT("Component"))
    {
        return EBlueprintNodeType::Component;
    }
    else
    {
        return EBlueprintNodeType::Other;
    }
}

TSharedPtr<FJsonObject> FBlueprintIndexTypes::NodeReferenceToJson(const FBlueprintNodeReference& NodeReference)
{
    TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
    JsonObject->SetStringField(TEXT("BlueprintName"), NodeReference.BlueprintName);
    JsonObject->SetStringField(TEXT("NodeType"), NodeTypeToString(NodeReference.NodeType));
    JsonObject->SetStringField(TEXT("NodeName"), NodeReference.NodeName);
    JsonObject->SetStringField(TEXT("NodeGuid"), NodeReference.NodeGuid);
    JsonObject->SetStringField(TEXT("DetailsPath"), NodeReference.DetailsPath);
    JsonObject->SetStringField(TEXT("BlueprintDetailsPath"), NodeReference.BlueprintDetailsPath);
    return JsonObject;
}

FBlueprintNodeReference FBlueprintIndexTypes::JsonToNodeReference(const TSharedPtr<FJsonObject>& JsonObject)
{
    FBlueprintNodeReference NodeReference;
    NodeReference.BlueprintName = JsonObject->GetStringField(TEXT("BlueprintName"));
    NodeReference.NodeType = StringToNodeType(JsonObject->GetStringField(TEXT("NodeType")));
    NodeReference.NodeName = JsonObject->GetStringField(TEXT("NodeName"));
    NodeReference.NodeGuid = JsonObject->GetStringField(TEXT("NodeGuid"));
    NodeReference.DetailsPath = JsonObject->GetStringField(TEXT("DetailsPath"));
    NodeReference.BlueprintDetailsPath = JsonObject->GetStringField(TEXT("BlueprintDetailsPath"));
    return NodeReference;
}

TSharedPtr<FJsonObject> FBlueprintIndexTypes::SearchResultToJson(const FBlueprintSearchResult& SearchResult)
{
    TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
    TArray<TSharedPtr<FJsonValue>> ResultsArray;
    
    for (const FBlueprintNodeReference& NodeReference : SearchResult.Results)
    {
        ResultsArray.Add(MakeShared<FJsonValueObject>(NodeReferenceToJson(NodeReference)));
    }
    
    JsonObject->SetArrayField(TEXT("Results"), ResultsArray);
    return JsonObject;
}

FBlueprintSearchResult FBlueprintIndexTypes::JsonToSearchResult(const TSharedPtr<FJsonObject>& JsonObject)
{
    FBlueprintSearchResult SearchResult;
    TArray<TSharedPtr<FJsonValue>> ResultsArray = JsonObject->GetArrayField(TEXT("Results"));
    
    for (const TSharedPtr<FJsonValue>& ResultValue : ResultsArray)
    {
        TSharedPtr<FJsonObject> ResultObject = ResultValue->AsObject();
        SearchResult.Results.Add(JsonToNodeReference(ResultObject));
    }
    
    return SearchResult;
}

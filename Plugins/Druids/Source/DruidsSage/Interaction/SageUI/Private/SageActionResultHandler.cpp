#include "SageActionResultHandler.h"
#include "ChatItems/DruidsSageActionRequestChatItem.h"

FSageActionResultHandler::FSageActionResultHandler(UDruidsSageActionRequestChatItem* InChatItem)
	: ChatItem(InChatItem)
{
}

void FSageActionResultHandler::HandleActionResult(bool bActionCalled, bool bSuccess, const FString& Result, const FString& ErrorMessage)
{
	if (ChatItem.IsValid())
	{
		ChatItem->HandleActionResult(bActionCalled, bSuccess, Result, ErrorMessage);
	}
}

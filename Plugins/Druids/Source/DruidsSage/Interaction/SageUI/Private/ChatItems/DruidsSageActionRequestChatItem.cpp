#include "ChatItems/DruidsSageActionRequestChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>

#include "DruidsSageMessagingHandler.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/ScrollBox.h"

UDruidsSageActionRequestChatItem::UDruidsSageActionRequestChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, RoleWidget(nullptr)
	, MessageWidget(nullptr)
	, ApplyButton(nullptr)
	, ApplyButtonText(nullptr)
	, ActionErrorMessage(nullptr)
	, FixErrorButton(nullptr)
	, ResponseMessage(nullptr)
	, ContentJsonValue(nullptr)
	, CurrentState(EActionRequestState::Ready)
	, ExecutionAttempts(0)
	, bIsInvocationAction(false)
{
}

void UDruidsSageActionRequestChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	SetupWidgets();
}

void UDruidsSageActionRequestChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	SetupWidgets();

	if (ApplyButton)
	{
		ApplyButton->OnClicked.AddDynamic(this, &UDruidsSageActionRequestChatItem::OnApplyButtonClicked);
	}

	if (FixErrorButton)
	{
		FixErrorButton->OnClicked.AddDynamic(this, &UDruidsSageActionRequestChatItem::OnFixErrorButtonClicked);
	}
}

void UDruidsSageActionRequestChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	SetupWidgets();
}

FName UDruidsSageActionRequestChatItem::GetTypeName() const
{
	return GetClassName();
}

void UDruidsSageActionRequestChatItem::FillInDruidsMessage(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(EDruidsSageChatRole::Assistant);

	// Include the action request content in the message
	if (ContentJsonValue.IsValid())
	{
		TArray<TSharedPtr<FJsonValue>> ContentArray;
		ContentArray.Add(ContentJsonValue);
		Message.SetContentArray(ContentArray);
	}
	else
	{
		// Fallback to empty content array if no JSON content is available
		TArray<TSharedPtr<FJsonValue>> EmptyContentArray;
		Message.SetContentArray(EmptyContentArray);
	}
}

EDruidsSageChatRole UDruidsSageActionRequestChatItem::GetMessageRole() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageActionRequestChatItem::GetMessagingHandler() const
{
	return MessagingHandler;
}

void UDruidsSageActionRequestChatItem::UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson)
{
	ContentJsonValue = ContentJson;

	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (!ContentJson.IsValid() || !ContentJson->TryGetObject(JsonObject) || !JsonObject)
	{
		return;
	}

	// Determine action type
	DetermineActionType(*JsonObject);

	FString Summary;
	if ((*JsonObject)->TryGetStringField(TEXT("summary"), Summary))
	{
		if (MessageWidget)
		{
			MessageWidget->SetText(FText::FromString(Summary));
		}
	}
}

void UDruidsSageActionRequestChatItem::InitializeActionRequestChatItem(const TScriptInterface<ISageExtensionDelegator>& InExtensionDelegator)
{
	ExtensionDelegator = InExtensionDelegator;
}

void UDruidsSageActionRequestChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
	MessagingHandler->SetFlags(RF_Standalone);
	MessagingHandler->ScrollBoxReference = InScrollBox;
}

void UDruidsSageActionRequestChatItem::OnApplyButtonClicked()
{
	// Check if button should be enabled for current state
	if (!IsButtonEnabledForState(CurrentState))
	{
		return;
	}

	// Handle retry logic for TryAgain state
	if (CurrentState == EActionRequestState::TryAgain)
	{
		if (!CanRetryAction())
		{
			return;
		}
	}

	// Increment execution attempts for invocation actions
	if (bIsInvocationAction)
	{
		ExecutionAttempts++;
	}

	// Set working state when action starts
	SetActionState(EActionRequestState::Working);

	// Hide error and response messages when starting work
	if (ActionErrorMessage)
	{
		ActionErrorMessage->SetVisibility(ESlateVisibility::Collapsed);
	}
	HideResponseMessage();

	if (OnActionApplied.IsBound() && ContentJsonValue.IsValid())
	{
		// Convert TSharedPtr<FJsonValue> to FString for Blueprint compatibility
		FString OutputString;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
		FJsonSerializer::Serialize(ContentJsonValue, TEXT(""), Writer);
		OnActionApplied.Broadcast(OutputString, this);
	}
}

void UDruidsSageActionRequestChatItem::OnFixErrorButtonClicked()
{
}

void UDruidsSageActionRequestChatItem::HandleActionResult(bool bActionCalled, bool bSuccess, const FString& Result, const FString& ErrorMessage)
{
	if (!bActionCalled)
	{
		// No action was taken, reset to ready state
		SetActionState(EActionRequestState::Ready);
		return;
	}

	if (bSuccess)
	{
		// Action succeeded - set Done state
		SetActionState(EActionRequestState::Done);

		// Hide error messages on success
		if (ActionErrorMessage)
		{
			ActionErrorMessage->SetVisibility(ESlateVisibility::Collapsed);
		}

		// Show response message for successful invocation actions
		if (bIsInvocationAction && !Result.IsEmpty())
		{
			ShowResponseMessage(Result);
		}
	}
	else
	{
		// Action failed - handle based on action type
		if (bIsInvocationAction)
		{
			// Invocation action failed - show generic error message
			if (ActionErrorMessage)
			{
				ActionErrorMessage->SetText(FText::FromString(TEXT("Action failed")));
				ActionErrorMessage->SetVisibility(ESlateVisibility::Visible);
			}

			if (ExecutionAttempts >= 2)
			{
				// Second failure - set Failed state
				SetActionState(EActionRequestState::Failed);
			}
			else
			{
				// First failure - set TryAgain state
				SetActionState(EActionRequestState::TryAgain);
			}
		}
		else
		{
			// Extension action failed - set Error state
			SetActionState(EActionRequestState::Error);

			// Show error message
			if (ActionErrorMessage && !ErrorMessage.IsEmpty())
			{
				ActionErrorMessage->SetText(FText::FromString(ErrorMessage));
				ActionErrorMessage->SetVisibility(ESlateVisibility::Visible);
			}
		}
	}

	// Broadcast the result for any listeners
	if (OnActionResult.IsBound())
	{
		OnActionResult.Broadcast(bActionCalled, bSuccess, Result, ErrorMessage);
	}
}

void UDruidsSageActionRequestChatItem::SetupWidgets()
{
	if (RoleWidget)
	{
		RoleWidget->SetText(FText::FromString(TEXT("Action Request:")));
	}

	// Initialize button state
	if (ApplyButtonText)
	{
		ApplyButtonText->SetText(GetButtonTextForState(CurrentState));
	}

	if (ApplyButton)
	{
		ApplyButton->SetIsEnabled(IsButtonEnabledForState(CurrentState));
	}

	// Hide error widgets by default
	if (ActionErrorMessage)
	{
		ActionErrorMessage->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (FixErrorButton)
	{
		FixErrorButton->SetVisibility(ESlateVisibility::Collapsed);
	}

	// Hide response message by default
	if (ResponseMessage)
	{
		ResponseMessage->SetVisibility(ESlateVisibility::Collapsed);
	}
}

FString UDruidsSageActionRequestChatItem::GetPlainText() const
{
	FString Summary;

	// Extract summary from ContentJsonValue if available
	if (ContentJsonValue.IsValid())
	{
		TSharedPtr<FJsonObject>* JsonObject = nullptr;
		if (ContentJsonValue->TryGetObject(JsonObject) && JsonObject)
		{
			(*JsonObject)->TryGetStringField(TEXT("summary"), Summary);
		}
	}

	// If no summary from JSON, try to get it from the MessageWidget
	if (Summary.IsEmpty() && MessageWidget)
	{
		Summary = MessageWidget->GetText().ToString();
	}

	// Return summary with action request indicator
	if (!Summary.IsEmpty())
	{
		return Summary + TEXT(" [Action Request Available]");
	}

	return TEXT("[Action Request Available]");
}

// State management functions
void UDruidsSageActionRequestChatItem::SetActionState(EActionRequestState NewState)
{
	if (CurrentState == NewState)
	{
		return;
	}

	CurrentState = NewState;

	// Update button text and enabled state
	if (ApplyButtonText)
	{
		ApplyButtonText->SetText(GetButtonTextForState(CurrentState));
	}

	if (ApplyButton)
	{
		ApplyButton->SetIsEnabled(IsButtonEnabledForState(CurrentState));
	}

	// Update widget visibility based on state
	// Note: Error message visibility is managed in HandleActionResult when setting specific error messages
}

FText UDruidsSageActionRequestChatItem::GetButtonTextForState(EActionRequestState State) const
{
	switch (State)
	{
	case EActionRequestState::Ready:
		return FText::FromString(TEXT("Action"));
	case EActionRequestState::Working:
		return FText::FromString(TEXT("Working"));
	case EActionRequestState::Done:
		return FText::FromString(TEXT("Done"));
	case EActionRequestState::Error:
		return FText::FromString(TEXT("Error"));
	case EActionRequestState::TryAgain:
		return FText::FromString(TEXT("Try Again"));
	case EActionRequestState::Failed:
		return FText::FromString(TEXT("Failed"));
	default:
		return FText::FromString(TEXT("Action"));
	}
}

bool UDruidsSageActionRequestChatItem::IsButtonEnabledForState(EActionRequestState State) const
{
	switch (State)
	{
	case EActionRequestState::Ready:
	case EActionRequestState::TryAgain:
		return true;
	case EActionRequestState::Working:
	case EActionRequestState::Done:
	case EActionRequestState::Error:
	case EActionRequestState::Failed:
		return false;
	default:
		return true;
	}
}

void UDruidsSageActionRequestChatItem::ResetActionState()
{
	CurrentState = EActionRequestState::Ready;
	ExecutionAttempts = 0;
	bIsInvocationAction = false;

	if (ApplyButtonText)
	{
		ApplyButtonText->SetText(GetButtonTextForState(CurrentState));
	}

	if (ApplyButton)
	{
		ApplyButton->SetIsEnabled(IsButtonEnabledForState(CurrentState));
	}

	// Hide error messages
	if (ActionErrorMessage)
	{
		ActionErrorMessage->SetVisibility(ESlateVisibility::Collapsed);
	}

	// Hide response messages
	HideResponseMessage();
}

bool UDruidsSageActionRequestChatItem::CanRetryAction() const
{
	return bIsInvocationAction && ExecutionAttempts < 2 && CurrentState == EActionRequestState::TryAgain;
}

void UDruidsSageActionRequestChatItem::DetermineActionType(const TSharedPtr<FJsonObject>& JsonObject)
{
	if (!JsonObject.IsValid())
	{
		return;
	}

	// Check for "extension_id" field to identify extension actions
	FString ExtensionId;
	if (JsonObject->TryGetStringField(TEXT("extension_id"), ExtensionId))
	{
		bIsInvocationAction = false;
		UE_LOG(LogTemp, Log, TEXT("UDruidsSageActionRequestChatItem: Detected extension action with ID: %s"), *ExtensionId);
		return;
	}

	// Check for "prompt" field to identify invocation actions
	FString Prompt;
	if (JsonObject->TryGetStringField(TEXT("prompt"), Prompt))
	{
		bIsInvocationAction = true;
		UE_LOG(LogTemp, Log, TEXT("UDruidsSageActionRequestChatItem: Detected invocation action with prompt"));
		return;
	}

	// Default to extension action if neither field is found
	bIsInvocationAction = false;
	UE_LOG(LogTemp, Warning, TEXT("UDruidsSageActionRequestChatItem: Could not determine action type, defaulting to extension action"));
}

void UDruidsSageActionRequestChatItem::ShowResponseMessage(const FString& Message)
{
	if (ResponseMessage)
	{
		ResponseMessage->SetText(FText::FromString(Message));
		ResponseMessage->SetVisibility(ESlateVisibility::Visible);
	}
}

void UDruidsSageActionRequestChatItem::HideResponseMessage()
{
	if (ResponseMessage)
	{
		ResponseMessage->SetVisibility(ESlateVisibility::Collapsed);
	}
}

#pragma once

#include <CoreMinimal.h>

#include "DruidsSageChatTypes.h"
#include "IDruidsSageChatItem.h"

#include "DruidsSageActionRequestChatItem.generated.h"

/**
 * Enumeration for action request button states
 */
UENUM(BlueprintType)
enum class EActionRequestState : uint8
{
	Ready,      // Initial state - "Action" button
	Working,    // Processing - "Working" button
	Done,       // Success - "Done" button (disabled)
	Error,      // Extension error - "Error" button (disabled)
	TryAgain,   // Invocation failure - "Try Again" button
	Failed      // Final failure - "Failed" button (disabled)
};

class ISageExtensionDelegator;
class UDruidsSageMessagingHandler;
class UTextBlock;
class UButton;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnActionAppliedUMG, const FString&, JsonString, UDruidsSageActionRequestChatItem*, ChatItem);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_FourParams(FOnActionResultUMG, bool, bActionCalled, bool, bSuccess, const FString&, Result, const FString&, ErrorMessage);

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageActionRequestChatItem : public UIDruidsSageChatItem
{
	GENERATED_BODY()

public:
	UDruidsSageActionRequestChatItem(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// UIDruidsSageChatItem interface implementation
	virtual FName GetTypeName() const override;
	virtual void FillInDruidsMessage(FDruidsSageChatMessage& Message) const override;
	virtual EDruidsSageChatRole GetMessageRole() const override;
	virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandler() const override;
	virtual void UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson) override;
	virtual FString GetPlainText() const override;

	// Initialization methods
	void InitializeActionRequestChatItem(const TScriptInterface<ISageExtensionDelegator>& InExtensionDelegator);

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetScrollBoxReference(class UScrollBox* InScrollBox);

	UFUNCTION()
	void HandleActionResult(bool bActionCalled, bool bSuccess, const FString& Result, const FString& ErrorMessage);

	// Getter for ApplyButton
	UButton* GetApplyButton() const { return ApplyButton; }

	// State management functions
	UFUNCTION(BlueprintCallable, Category = "Action State")
	void SetActionState(EActionRequestState NewState);

	UFUNCTION(BlueprintPure, Category = "Action State")
	EActionRequestState GetCurrentState() const { return CurrentState; }

	UFUNCTION(BlueprintPure, Category = "Action State")
	FText GetButtonTextForState(EActionRequestState State) const;

	UFUNCTION(BlueprintPure, Category = "Action State")
	bool IsButtonEnabledForState(EActionRequestState State) const;

	UFUNCTION(BlueprintCallable, Category = "Action State")
	void ResetActionState();

	UFUNCTION(BlueprintPure, Category = "Action State")
	bool CanRetryAction() const;

	// Response message functions
	UFUNCTION(BlueprintCallable, Category = "Action Response")
	void ShowResponseMessage(const FString& Message);

	UFUNCTION(BlueprintCallable, Category = "Action Response")
	void HideResponseMessage();

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Chat Item")
	FOnActionAppliedUMG OnActionApplied;

	UPROPERTY(BlueprintAssignable, Category = "Chat Item")
	FOnActionResultUMG OnActionResult;

	static FName GetClassName() { return "UDruidsSageActionRequestChatItem"; }

protected:
	// BindWidget properties for Blueprint binding
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* RoleWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* MessageWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UButton* ApplyButton;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* ApplyButtonText;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* ActionErrorMessage;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UButton* FixErrorButton;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* ResponseMessage;

private:
	TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler;
	TScriptInterface<ISageExtensionDelegator> ExtensionDelegator;
	TSharedPtr<FJsonValue> ContentJsonValue;

	// State management variables
	EActionRequestState CurrentState;
	int32 ExecutionAttempts;
	bool bIsInvocationAction;

	UFUNCTION()
	void OnApplyButtonClicked();

	UFUNCTION()
	void OnFixErrorButtonClicked();

	void SetupWidgets();
	void DetermineActionType(const TSharedPtr<FJsonObject>& JsonObject);
};

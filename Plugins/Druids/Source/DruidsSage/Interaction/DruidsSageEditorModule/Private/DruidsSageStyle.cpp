#include "DruidsSageStyle.h"
#include "Styling/SlateStyleRegistry.h"
#include "Framework/Application/SlateApplication.h"
#include "Slate/SlateGameResources.h"
#include "Interfaces/IPluginManager.h"
#include "Styling/SlateStyleMacros.h"

#define RootToContentDir Style->RootToContentDir

TSharedPtr<FSlateStyleSet> FDruidsSageStyle::StyleInstance = nullptr;

void FDruidsSageStyle::Initialize()
{
	if (!StyleInstance.IsValid())
	{
		StyleInstance = Create();
		FSlateStyleRegistry::RegisterSlateStyle(*StyleInstance);
	}
}

void FDruidsSageStyle::Shutdown()
{
	FSlateStyleRegistry::UnRegisterSlateStyle(*StyleInstance);
	ensure(StyleInstance.IsUnique());
	StyleInstance.Reset();
}

FName FDruidsSageStyle::GetStyleSetName()
{
	static FName StyleSetName(TEXT("DruidsSageStyle"));
	return StyleSetName;
}

const FVector2D Icon16x16(16.0f, 16.0f);
const FVector2D Icon20x20(20.0f, 20.0f);

TSharedRef< FSlateStyleSet > FDruidsSageStyle::Create()
{
	TSharedRef< FSlateStyleSet > Style = MakeShareable(new FSlateStyleSet("DruidsSageStyle"));
	Style->SetContentRoot(IPluginManager::Get().FindPlugin("Druids")->GetBaseDir() / TEXT("Content"));

	Style->Set("DruidsSage.ChatIcon", new IMAGE_BRUSH(TEXT("sageUEbutton"), Icon20x20));

	return Style;
}

void FDruidsSageStyle::ReloadTextures()
{
	if (FSlateApplication::IsInitialized())
	{
		FSlateApplication::Get().GetRenderer()->ReloadTextureResources();
	}
}

const ISlateStyle& FDruidsSageStyle::Get()
{
	return *StyleInstance;
}

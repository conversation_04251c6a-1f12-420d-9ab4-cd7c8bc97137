#include "SageExtensionDelegator.h"

#include "SageExtension.h"
#include "ActiveSageExtensions.h"
#include "SimpleJSON.h"
#include "DruidsSage/Features/SagePython/Public/SagePythonTypes.h"

FSageActionResult FSageExtensionDelegator::OnActionApplied(const TSharedPtr<FJsonValue>& ActionDetails)
{
	if (const TSharedPtr<FJsonObject> *ContentObject;
		ActionDetails->TryGetObject(ContentObject) && ContentObject->IsValid())
	{
		if (FString ExtensionId;
			ContentObject->Get()->TryGetStringField(TEXT("extension_id"), ExtensionId))
		{
			TWeakObjectPtr<USageExtension> DruidsSageExtension =
				FActiveSageExtensions::Get().GetExtensionForId(ExtensionId);
			if (DruidsSageExtension.IsValid())
			{
				try
				{
					DruidsSageExtension.Get()->ExecuteAction(ContentObject->ToWeakPtr());

					// Check if the extension reported an error
					if (USageExtension::HasExtensionError())
					{
						FString ErrorMessage = USageExtension::GetExtensionError();
						return FSageActionResult::ExtensionFailure(ErrorMessage);
					}

					return FSageActionResult::ExtensionSuccess(FString::Printf(TEXT("Extension '%s' executed successfully"), *ExtensionId));
				}
				catch (const std::exception& e)
				{
					FString ErrorMessage = FString::Printf(TEXT("Extension '%s' execution failed: %s"), *ExtensionId, UTF8_TO_TCHAR(e.what()));
					return FSageActionResult::ExtensionFailure(ErrorMessage);
				}
				catch (...)
				{
					FString ErrorMessage = FString::Printf(TEXT("Extension '%s' execution failed with unknown error"), *ExtensionId);
					return FSageActionResult::ExtensionFailure(ErrorMessage);
				}
			}
			else
			{
				FString ErrorMessage = FString::Printf(TEXT("Extension with ID '%s' not found or invalid"), *ExtensionId);
				return FSageActionResult::ExtensionFailure(ErrorMessage);
			}
		}
	}

	// No extension action was taken
	return FSageActionResult::NoAction();
}

TSharedPtr<FJsonValue> FSageExtensionDelegator::OnQueryRequested(const TSharedPtr<FJsonObject>& QueryRequestObject)
{
	if (FString ExtensionId;
	QueryRequestObject.Get()->TryGetStringField(TEXT("extension_id"), ExtensionId))
	{
		TWeakObjectPtr<USageExtension> DruidsSageExtension =
			FActiveSageExtensions::Get().GetExtensionForId(ExtensionId);
		if (DruidsSageExtension.IsValid())
		{
			TSharedPtr<FJsonValue> Results = DruidsSageExtension.Get()->ExecuteQuery(QueryRequestObject.ToWeakPtr());

			if (Results.IsValid())
			{
				return Results;
			}

			return SimpleJSON("{ \"error\" : \"Invalid results returned\" }").GetJsonValue();
		}
	}

	return SimpleJSON("{ \"error\" : \"Invalid extension id\" }").GetJsonValue();
}

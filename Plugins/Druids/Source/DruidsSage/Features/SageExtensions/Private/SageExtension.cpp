#include "SageExtension.h"

#include "AssetToolsModule.h"
#include "IAssetTools.h"

#include "LogDruids.h"

#include "ISageActiveObjectProvider.h"
#include "SimpleJSON.h"


FString USageExtension::BuildFullPath(const EDruidsSagePathPrefix Prefix, const FString& ModuleName,
                                      const FString& ClassName)
{
	FString PrefixStr;
	switch (Prefix)
	{
	case EDruidsSagePathPrefix::Script:
		PrefixStr = TEXT("/Script/");
		break;
	//TODO
	/*
            case EDruidsSagePathPrefix::Game:
                PrefixStr = TEXT("/Game/");
                break;
            case EDruidsSagePathPrefix::Engine:
                PrefixStr = TEXT("/Engine/");
                break;
            case EDruidsSagePathPrefix::Extension:
                PrefixStr = TEXT("/Extension/");
                break;
            */
	}
        
	return FString::Printf(TEXT("%s%s.%s"), *PrefixStr, *ModuleName, *ClassName);
}

bool AreActionsEqual(const TArray<FDruidsSageExtensionActionDefinition>& A, const TArray<FDruidsSageExtensionActionDefinition>& B)
{
    if (A.Num() != B.Num())
        return false;
        
    for (int32 i = 0; i < A.Num(); i++)
    {
        if (A[i].FunctionName != B[i].FunctionName ||
            A[i].Description != B[i].Description ||
            A[i].ParameterDefinitions.Num() != B[i].ParameterDefinitions.Num())
            return false;
            
        for (int32 j = 0; j < A[i].ParameterDefinitions.Num(); j++)
        {
            const auto& ParamA = A[i].ParameterDefinitions[j];
            const auto& ParamB = B[i].ParameterDefinitions[j];
            
            if (ParamA.Name != ParamB.Name ||
                ParamA.Description != ParamB.Description ||
                ParamA.ParameterType != ParamB.ParameterType)
                return false;
        }
    }
    return true;
}

bool AreQueriesEqual(const TArray<FDruidsSageExtensionQueryDefinition>& A, const TArray<FDruidsSageExtensionQueryDefinition>& B)
{
    if (A.Num() != B.Num())
        return false;
        
    for (int32 i = 0; i < A.Num(); i++)
    {
        if (A[i].FunctionName != B[i].FunctionName ||
            A[i].Description != B[i].Description ||
            A[i].ParameterDefinitions.Num() != B[i].ParameterDefinitions.Num() ||
            A[i].ResultDefinitions.Num() != B[i].ResultDefinitions.Num())
            return false;
            
        for (int32 j = 0; j < A[i].ParameterDefinitions.Num(); j++)
        {
            const auto& ParamA = A[i].ParameterDefinitions[j];
            const auto& ParamB = B[i].ParameterDefinitions[j];
            
            if (ParamA.Name != ParamB.Name ||
                ParamA.Description != ParamB.Description ||
                ParamA.ParameterType != ParamB.ParameterType)
                return false;
        }
        
        for (int32 j = 0; j < A[i].ResultDefinitions.Num(); j++)
        {
            const auto& ResultA = A[i].ResultDefinitions[j];
            const auto& ResultB = B[i].ResultDefinitions[j];
            
            if (ResultA.Name != ResultB.Name ||
                ResultA.Description != ResultB.Description ||
                ResultA.ParameterType != ResultB.ParameterType)
                return false;
        }
    }
    return true;
}

void USageExtension::RefreshExtension()
{
	// If this is the base class itself, don't do anything
	if (GetClass() == StaticClass())
	{
		return;
	}

	// If this is not the CDO, don't do anything
	if (!HasAnyFlags(RF_ClassDefaultObject))
	{
		return;
	}

	Definition.ExtensionId = GetClass()->GetPathName();

	TArray<FDruidsSageExtensionActionDefinition> OldActions = Definition.Actions;
	TArray<FDruidsSageExtensionQueryDefinition> OldQueries = Definition.Queries;
    
	// Discover new actions and queries
	DiscoverActionsAndQueries();
    
	// Compare using proper equality checks
	if (!AreActionsEqual(OldActions, Definition.Actions) || !AreQueriesEqual(OldQueries, Definition.Queries))
	{
		MarkPackageDirty();
	}
}

TSharedPtr<FDruidsSageExtensionDefinition> USageExtension::GetExtensionDefinition() const
{
	return MakeShared<FDruidsSageExtensionDefinition>(Definition);
}

void USageExtension::DiscoverActionsAndQueries()
{
	// Store existing parameter descriptions before clearing
	TMap<FString, FString> ExistingDescriptions;
	
	// Save descriptions from Actions
	for (const FDruidsSageExtensionActionDefinition& Action : Definition.Actions)
	{
		for (const FDruidsSageExtensionParameterDefinition& Param : Action.ParameterDefinitions)
		{
			if (!Param.Description.IsEmpty())
			{
				// Create a unique key combining function name and parameter name
				FString Key = FString::Printf(TEXT("Action_%s_%s"), 
					*Action.FunctionName.ToString(), 
					*Param.Name.ToString());
				ExistingDescriptions.Add(Key, Param.Description);
			}
		}
	}
	
	// Save descriptions from Queries (both parameters and results)
	for (const FDruidsSageExtensionQueryDefinition& Query : Definition.Queries)
	{
		for (const FDruidsSageExtensionParameterDefinition& Param : Query.ParameterDefinitions)
		{
			if (!Param.Description.IsEmpty())
			{
				FString Key = FString::Printf(TEXT("Query_Param_%s_%s"), 
					*Query.FunctionName.ToString(), 
					*Param.Name.ToString());
				ExistingDescriptions.Add(Key, Param.Description);
			}
		}
		
		for (const FDruidsSageExtensionParameterDefinition& Result : Query.ResultDefinitions)
		{
			if (!Result.Description.IsEmpty())
			{
				FString Key = FString::Printf(TEXT("Query_Result_%s_%s"), 
					*Query.FunctionName.ToString(), 
					*Result.Name.ToString());
				ExistingDescriptions.Add(Key, Result.Description);
			}
		}
	}

	// Clear existing actions and queries
	Definition.Actions.Empty();
	Definition.Queries.Empty();

	// Get all functions in this class
	for (TFieldIterator<UFunction> FuncIt(GetClass()); FuncIt; ++FuncIt)
	{
		UFunction* Function = *FuncIt;
		DiscoverFunction(Function, ExistingDescriptions);
	}
}

void USageExtension::DiscoverFunction(UFunction* Function, const TMap<FString, FString>& ExistingDescriptions)
{
	FString MetaData = Function->GetMetaData(TEXT("Category"));
	MetaData.ReplaceInline(TEXT(" "), TEXT(""));
	
	bool bIsAction = MetaData.Contains(TEXT("DruidSageActions"));
	bool bIsQuery = MetaData.Contains(TEXT("DruidSageQueries"));
	
	if (!bIsAction && !bIsQuery)
		return;

	FString DisplayName = Function->GetMetaData(TEXT("DisplayName"));
	FString Description = Function->GetMetaData(TEXT("ToolTip"));
	
	if (bIsAction)
	{
		FDruidsSageExtensionActionDefinition Action;
		Action.FunctionName = DisplayName.IsEmpty() ? Function->GetFName() : FName(*DisplayName);
		Action.Description = Description;

		for (TFieldIterator<FProperty> It(Function); It && (It->PropertyFlags & CPF_Parm); ++It)
		{
			FProperty* Prop = *It;
			
			if (Prop->HasAnyPropertyFlags(CPF_ReturnParm))
				continue;

			FDruidsSageExtensionParameterDefinition Param;
			Param.Name = Prop->GetFName();
			Param.ParameterType = FDruidsSageExtensionParameterDefinition::GetParameterTypeForProperty(Prop);
			if (Param.ParameterType == EDruidsSageExtensionParameterType::Array)
			{
				const FArrayProperty* ArrayProp = CastField<FArrayProperty>(Prop);
				Param.ArrayElementType = GetArrayInnerType(ArrayProp);
			}
			else if (Param.ParameterType == EDruidsSageExtensionParameterType::CustomStruct)
			{
				const FStructProperty* StructProp = CastField<FStructProperty>(Prop);
				Param.StructElementType = StructProp->Struct;
			}
			
			// Restore description if it exists
			FString Key = FString::Printf(TEXT("Action_%s_%s"), 
				*Action.FunctionName.ToString(), 
				*Param.Name.ToString());
			if (const FString* ExistingDesc = ExistingDescriptions.Find(Key))
			{
				Param.Description = *ExistingDesc;
			}
			
			Action.ParameterDefinitions.Add(Param);
		}

		Definition.Actions.Add(Action);
	}
	else // Query
	{
		FDruidsSageExtensionQueryDefinition Query;
		Query.FunctionName = Function->GetFName();
		Query.Description = Description;

		// Discover parameters (input)
		for (TFieldIterator<FProperty> It(Function); It && (It->PropertyFlags & CPF_Parm); ++It)
		{
			FProperty* Prop = *It;
			
			if (Prop->HasAnyPropertyFlags(CPF_ReturnParm | CPF_OutParm))
				continue;

			FDruidsSageExtensionParameterDefinition Param;
			Param.Name = Prop->GetFName();
			Param.ParameterType = FDruidsSageExtensionParameterDefinition::GetParameterTypeForProperty(Prop);
			if (Param.ParameterType == EDruidsSageExtensionParameterType::Array)
			{
				const FArrayProperty* ArrayProp = CastField<FArrayProperty>(Prop);
				Param.ArrayElementType = GetArrayInnerType(ArrayProp);
			}
			else if (Param.ParameterType == EDruidsSageExtensionParameterType::CustomStruct)
			{
				const FStructProperty* StructProp = CastField<FStructProperty>(Prop);
				Param.StructElementType = StructProp->Struct;
			}
			
			// Restore description if it exists
			FString Key = FString::Printf(TEXT("Query_Param_%s_%s"), 
				*Query.FunctionName.ToString(), 
				*Param.Name.ToString());
			if (const FString* ExistingDesc = ExistingDescriptions.Find(Key))
			{
				Param.Description = *ExistingDesc;
			}
			
			Query.ParameterDefinitions.Add(Param);
		}

		// Discover results (output parameters)
		for (TFieldIterator<FProperty> It(Function); It && (It->PropertyFlags & CPF_Parm); ++It)
		{
			FProperty* Prop = *It;
			
			if (!Prop->HasAnyPropertyFlags(CPF_OutParm) || Prop->HasAnyPropertyFlags(CPF_ReturnParm))
				continue;

			FDruidsSageExtensionParameterDefinition Result;
			Result.Name = Prop->GetFName();
			Result.ParameterType = FDruidsSageExtensionParameterDefinition::GetParameterTypeForProperty(Prop);
			if (Result.ParameterType == EDruidsSageExtensionParameterType::Array)
			{
				const FArrayProperty* ArrayProp = CastField<FArrayProperty>(Prop);
				Result.ArrayElementType = GetArrayInnerType(ArrayProp);
			}
			else if (Result.ParameterType == EDruidsSageExtensionParameterType::CustomStruct)
			{
				const FStructProperty* StructProp = CastField<FStructProperty>(Prop);
				Result.StructElementType = StructProp->Struct;
			}
			
			// Restore description if it exists
			FString Key = FString::Printf(TEXT("Query_Result_%s_%s"), 
				*Query.FunctionName.ToString(), 
				*Result.Name.ToString());
			if (const FString* ExistingDesc = ExistingDescriptions.Find(Key))
			{
				Result.Description = *ExistingDesc;
			}
			
			Query.ResultDefinitions.Add(Result);
		}

		Definition.Queries.Add(Query);
	}
}

EDruidsSageExtensionParameterType USageExtension::GetArrayInnerType(const FArrayProperty* ArrayProperty)
{
	if (ArrayProperty)
	{
		return FDruidsSageExtensionParameterDefinition::GetParameterTypeForProperty(ArrayProperty->Inner);
	}
	return EDruidsSageExtensionParameterType::None;
}

void USageExtension::PostInitProperties()
{
	Super::PostInitProperties();

	PopulateAssetTypes();
}

void USageExtension::PostCDOCompiled(const FPostCDOCompiledContext& Context)
{
	Super::PostCDOCompiled(Context);

	// Skip the base class itself
	if (GetClass() == StaticClass())
	{
		return;
	}

	RefreshExtension();
}

void USageExtension::PopulateAssetTypes()
{
	AssetTypeMap.Empty();
        
	// Get asset tools
	IAssetTools& AssetTools = FModuleManager::LoadModuleChecked<FAssetToolsModule>("AssetTools").Get();
        
	// Get all asset type actions
	TArray<TWeakPtr<IAssetTypeActions>> AssetTypeActions;
	AssetTools.GetAssetTypeActionsList(AssetTypeActions);

	// Add common asset types
	for (TWeakPtr ActionPtr : AssetTypeActions)
	{
		if (const TSharedPtr<IAssetTypeActions> Action = ActionPtr.Pin())
		{
			if (const UClass* SupportedClass = Action->GetSupportedClass())
			{
				FString DisplayName = Action->GetName().ToString();
				FString ClassPath = SupportedClass->GetPathName();
				FString Left, Right;
				FString LocalModuleName, LocalClassName;
                    
				if (ClassPath.Split(TEXT("/Script/"), &Left, &Right))
				{
					Right.Split(TEXT("."), &LocalModuleName, &LocalClassName);
					AssetTypeMap.Add(DisplayName, FDruidsSageExtensionAssetTypeData(DisplayName, LocalModuleName, LocalClassName));
				}
			}
		}
	}
}

TArray<FString> USageExtension::GetAvailableAssetTypes() const
{
	TArray<FString> Types;
	AssetTypeMap.GetKeys(Types);
	Types.Sort();
	return Types;
}

void USageExtension::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.Property && 
		PropertyChangedEvent.Property->GetFName() == GET_MEMBER_NAME_CHECKED(FDruidsSageExtensionDefinition, SelectedAssetType))
	{
		if (const FDruidsSageExtensionAssetTypeData* TypeData = AssetTypeMap.Find(Definition.SelectedAssetType))
		{
			ModuleName = TypeData->ModuleName;
			ClassName = TypeData->ClassName;
			PathPrefix = EDruidsSagePathPrefix::Script;
		}
	}
}

UObject* USageExtension::GetCurrentlyEditedAsset()
{
	if (ISageActiveObjectProvider* EditorInterface = FSageActiveObjectProvider::Get())
	{
		if (EditorInterface->GetActiveObject().IsValid())
		{
			return EditorInterface->GetActiveObject().Get();
		}
	}

	// Fallback to using AssetEditorSubsystem if the module's tracking fails
	if (GEditor)
	{
		if (UAssetEditorSubsystem* AssetEditorSubsystem = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>())
		{
			TArray<UObject*> EditedAssets = AssetEditorSubsystem->GetAllEditedAssets();
			if (EditedAssets.Num() > 0)
			{
				return EditedAssets[0];
			}
		}
	}

	return nullptr;
}

UWorld* USageExtension::GetWorldEditorContext()
{
	if (GEditor)
	{
		return GEditor->GetEditorWorldContext().World();
	}
	return nullptr;
}

// Thread-local storage for extension errors
thread_local FString USageExtension::CurrentExtensionError;

void USageExtension::ReportExtensionError(const FString& ErrorMessage)
{
	CurrentExtensionError = ErrorMessage;
	UE_LOG(LogDruidsSage_Internal, Warning, TEXT("Extension error reported: %s"), *ErrorMessage);
}

void USageExtension::ClearExtensionError()
{
	CurrentExtensionError.Empty();
}

FString USageExtension::GetExtensionError()
{
	return CurrentExtensionError;
}

bool USageExtension::HasExtensionError()
{
	return !CurrentExtensionError.IsEmpty();
}

bool USageExtension::MatchesContext(const TWeakObjectPtr<> ActiveObject) const
{
	if (Definition.ContextType == EDruidsSageExtensionContextType::Any)
		return true;
            
	if (ActiveObject.IsValid() == false)
		return false;

	FString FullPath = BuildFullPath(PathPrefix, ModuleName, ClassName);
	UClass* ContextClass = FindObject<UClass>(nullptr, *FullPath);
	return ContextClass && ActiveObject.Get()->IsA(ContextClass);
}

void USageExtension::ExecuteAction(TWeakPtr<FJsonObject> ActionRequestDetails)
{
	// Clear any previous extension errors
	ClearExtensionError();

	FString DefinitionString;
	const TSharedRef<TJsonWriter<>> DefiniitonWriter = TJsonWriterFactory<>::Create(&DefinitionString);
	FJsonSerializer::Serialize(Definition.GetExtensionDefinitionJson().ToSharedRef(), DefiniitonWriter);

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("USageExtension::ExecuteAction() - Extension Definition: %s"), *DefinitionString);

    if (!ActionRequestDetails.IsValid())
        return;

	FString ActionDetailsString;
	const TSharedRef<TJsonWriter<>> ActionDetailsWriter = TJsonWriterFactory<>::Create(&ActionDetailsString);
	FJsonSerializer::Serialize(ActionRequestDetails.Pin().ToSharedRef(), ActionDetailsWriter);

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("USageExtension::ExecuteAction() - Action Request Details: %s"), *ActionDetailsString);

    TSharedPtr<FJsonObject> Details = ActionRequestDetails.Pin();
    
    // Extract action name from the request
    FString ActionName;
    if (!Details->TryGetStringField(TEXT("action_name"), ActionName))
        return;
        
    // Find matching action in our definition
    for (const FDruidsSageExtensionActionDefinition& Action : Definition.Actions)
    {
        if (Action.FunctionName.ToString() == ActionName)
        {
            // Found matching action, now prepare to call the function
            UFunction* Function = GetClass()->FindFunctionByName(FName(*ActionName));
            if (!Function)
                return;
                
            // Extract parameters from JSON
            const TSharedPtr<FJsonObject>* ParametersObj;
            if (Details->TryGetObjectField(TEXT("parameters"), ParametersObj))
            {
                // Create a buffer for function parameters
                void* Params = FMemory::Malloc(Function->ParmsSize);
                FMemory::Memzero(Params, Function->ParmsSize);
                
                // Fill in parameters from JSON
                for (TFieldIterator<FProperty> It(Function); It && (It->PropertyFlags & CPF_Parm); ++It)
                {
                    FProperty* Prop = *It;
                    
                    if (Prop->HasAnyPropertyFlags(CPF_ReturnParm))
                        continue;
                        
                    void* ValuePtr = Prop->ContainerPtrToValuePtr<void>(Params);
                    FString ParamName = Prop->GetName();
                    
                    // Handle array properties
                    if (Prop->IsA<FArrayProperty>())
                    {
                        FArrayProperty* ArrayProp = CastField<FArrayProperty>(Prop);
                        const TArray<TSharedPtr<FJsonValue>>* JsonArray;
                        
                        if (ParametersObj->Get()->TryGetArrayField(ParamName, JsonArray) && JsonArray->Num() > 0)
                        {
                            FScriptArrayHelper ArrayHelper(ArrayProp, ValuePtr);
                            ArrayHelper.EmptyValues();
                            
                            for (int32 i = 0; i < JsonArray->Num(); ++i)
                            {
                                int32 NewIndex = ArrayHelper.AddValue();
                                void* ElementPtr = ArrayHelper.GetRawPtr(NewIndex);
                                
                                // Use our helper function to set the element value
                                SetPropertyValueFromJson(ArrayProp->Inner, ElementPtr, (*JsonArray)[i]);
                            }
                        }
                    }
                    // Handle non-array properties
                    else
                    {
                        // Create a JSON value wrapper for the field
                        TSharedPtr<FJsonValue> JsonValue;
                        
                        // Try to get the appropriate JSON value based on property type
                        if (Prop->IsA<FStrProperty>() || Prop->IsA<FNameProperty>() || Prop->IsA<FTextProperty>())
                        {
                            FString StringValue;
                            if (ParametersObj->Get()->TryGetStringField(ParamName, StringValue))
                            {
                                JsonValue = MakeShared<FJsonValueString>(StringValue);
                            }
                        }
                        else if (Prop->IsA<FIntProperty>() || Prop->IsA<FFloatProperty>() || Prop->IsA<FDoubleProperty>())
                        {
                            double NumberValue;
                            if (ParametersObj->Get()->TryGetNumberField(ParamName, NumberValue))
                            {
                                JsonValue = MakeShared<FJsonValueNumber>(NumberValue);
                            }
                        }
                        else if (Prop->IsA<FBoolProperty>())
                        {
                            bool BoolValue;
                            if (ParametersObj->Get()->TryGetBoolField(ParamName, BoolValue))
                            {
                                JsonValue = MakeShared<FJsonValueBoolean>(BoolValue);
                            }
                        }
                        else if (Prop->IsA<FStructProperty>())
                        {
                            const TSharedPtr<FJsonObject>* ObjectValue;
                            if (ParametersObj->Get()->TryGetObjectField(ParamName, ObjectValue))
                            {
                                JsonValue = MakeShared<FJsonValueObject>(ObjectValue->ToSharedRef());
                            }
                            else
                            {
                                const TArray<TSharedPtr<FJsonValue>>* ArrayValue;
                                if (ParametersObj->Get()->TryGetArrayField(ParamName, ArrayValue))
                                {
                                    JsonValue = MakeShared<FJsonValueArray>(*ArrayValue);
                                }
                            }
                        }
                        
                        // If we got a JSON value, set the property
                        if (JsonValue.IsValid())
                        {
                            SetPropertyValueFromJson(Prop, ValuePtr, JsonValue);
                        }
                    }
                }

	            {
		            // Wrap the function call in an undo transaction
                	FScopedTransaction Transaction(FText::Format(NSLOCTEXT("DruidsSage", "ExecuteAction", "Execute Druids Action: {0}"), FText::FromString(ActionName)));
                
                	// Call the function within the transaction scope
                	ProcessEvent(Function, Params);
	            }
                
                // Clean up
                for (TFieldIterator<FProperty> It(Function); It && (It->PropertyFlags & CPF_Parm); ++It)
                {
                    It->DestroyValue_InContainer(Params);
                }
                FMemory::Free(Params);
            }
            
            break;
        }
    }
}

TSharedPtr<FJsonValue> USageExtension::ExecuteQuery(TWeakPtr<FJsonObject> QueryRequestDetails)
{
    FString DefinitionString;
    const TSharedRef<TJsonWriter<>> DefinitionWriter = TJsonWriterFactory<>::Create(&DefinitionString);
    FJsonSerializer::Serialize(Definition.GetExtensionDefinitionJson().ToSharedRef(), DefinitionWriter);

    UE_LOG(LogDruidsSage_Internal, Display, TEXT("USageExtension::ExecuteQuery() - Extension Definition: %s"), *DefinitionString);

    if (!QueryRequestDetails.IsValid())
    {
    	return SimpleJSON("{ \"error\" : \"query request invalid\" }").GetJsonValue();
    }

    FString QueryDetailsString;
    const TSharedRef<TJsonWriter<>> QueryDetailsWriter = TJsonWriterFactory<>::Create(&QueryDetailsString);
    FJsonSerializer::Serialize(QueryRequestDetails.Pin().ToSharedRef(), QueryDetailsWriter);
    
    UE_LOG(LogDruidsSage_Internal, Display, TEXT("USageExtension::ExecuteQuery() - Query Request Details: %s"), *QueryDetailsString);
        
    TSharedPtr<FJsonObject> Details = QueryRequestDetails.Pin();
    
    // Extract query name from the request
    FString QueryName;
    if (!Details->TryGetStringField(TEXT("query_name"), QueryName))
    {
    	return SimpleJSON("{ \"error\" : \"query name missing\" }").GetJsonValue();
    }
        
    // Find matching query in our definition
    for (const FDruidsSageExtensionQueryDefinition& Query : Definition.Queries)
    {
        if (Query.FunctionName.ToString() == QueryName)
        {
            // Found matching query, now prepare to call the function
            UFunction* Function = GetClass()->FindFunctionByName(FName(*QueryName));
            if (!Function)
            {
		    	return SimpleJSON("{ \"error\" : \"query function not found\" }").GetJsonValue();
            }
                
            // Extract parameters from JSON
            const TSharedPtr<FJsonObject>* ParametersObj;
            if (Details->TryGetObjectField(TEXT("parameters"), ParametersObj))
            {
                // Create a buffer for function parameters
                void* Params = FMemory::Malloc(Function->ParmsSize);
                FMemory::Memzero(Params, Function->ParmsSize);
                
                // Fill in parameters from JSON
                for (TFieldIterator<FProperty> It(Function); It && (It->PropertyFlags & CPF_Parm); ++It)
                {
                    FProperty* Prop = *It;
                    
                    if (Prop->HasAnyPropertyFlags(CPF_ReturnParm | CPF_OutParm))
                        continue;
                        
                    void* ValuePtr = Prop->ContainerPtrToValuePtr<void>(Params);
                    FString ParamName = Prop->GetName();
                    
                    // Handle array properties
                    if (Prop->IsA<FArrayProperty>())
                    {
                        FArrayProperty* ArrayProp = CastField<FArrayProperty>(Prop);
                        const TArray<TSharedPtr<FJsonValue>>* JsonArray;
                        
                        if (ParametersObj->Get()->TryGetArrayField(ParamName, JsonArray) && JsonArray->Num() > 0)
                        {
                            FScriptArrayHelper ArrayHelper(ArrayProp, ValuePtr);
                            ArrayHelper.EmptyValues();
                            
                            for (int32 i = 0; i < JsonArray->Num(); ++i)
                            {
                                int32 NewIndex = ArrayHelper.AddValue();
                                void* ElementPtr = ArrayHelper.GetRawPtr(NewIndex);
                                
                                // Use our helper function to set the element value
                                SetPropertyValueFromJson(ArrayProp->Inner, ElementPtr, (*JsonArray)[i]);
                            }
                        }
                    }
                    // Handle non-array properties
                    else
                    {
                        // Create a JSON value wrapper for the field
                        TSharedPtr<FJsonValue> JsonValue;
                        
                        // Try to get the appropriate JSON value based on property type
                        if (Prop->IsA<FStrProperty>() || Prop->IsA<FNameProperty>() || Prop->IsA<FTextProperty>())
                        {
                            FString StringValue;
                            if (ParametersObj->Get()->TryGetStringField(ParamName, StringValue))
                            {
                                JsonValue = MakeShared<FJsonValueString>(StringValue);
                            }
                        }
                        else if (Prop->IsA<FIntProperty>() || Prop->IsA<FFloatProperty>() || Prop->IsA<FDoubleProperty>())
                        {
                            double NumberValue;
                            if (ParametersObj->Get()->TryGetNumberField(ParamName, NumberValue))
                            {
                                JsonValue = MakeShared<FJsonValueNumber>(NumberValue);
                            }
                        }
                        else if (Prop->IsA<FBoolProperty>())
                        {
                            bool BoolValue;
                            if (ParametersObj->Get()->TryGetBoolField(ParamName, BoolValue))
                            {
                                JsonValue = MakeShared<FJsonValueBoolean>(BoolValue);
                            }
                        }
                        else if (Prop->IsA<FStructProperty>())
                        {
                            const TSharedPtr<FJsonObject>* ObjectValue;
                            if (ParametersObj->Get()->TryGetObjectField(ParamName, ObjectValue))
                            {
                                JsonValue = MakeShared<FJsonValueObject>(ObjectValue->ToSharedRef());
                            }
                            else
                            {
                                const TArray<TSharedPtr<FJsonValue>>* ArrayValue;
                                if (ParametersObj->Get()->TryGetArrayField(ParamName, ArrayValue))
                                {
                                    JsonValue = MakeShared<FJsonValueArray>(*ArrayValue);
                                }
                            }
                        }
                        
                        // If we got a JSON value, set the property
                        if (JsonValue.IsValid())
                        {
                            SetPropertyValueFromJson(Prop, ValuePtr, JsonValue);
                        }
                    }
                }

                // Call the function
                ProcessEvent(Function, Params);
                
                // Create a JSON object to hold the results
            	SimpleJSON Results;
                
                // Extract out parameters (results)
                for (TFieldIterator<FProperty> It(Function); It && (It->PropertyFlags & CPF_Parm); ++It)
                {
                    FProperty* Prop = *It;
                    
                    if (!Prop->HasAnyPropertyFlags(CPF_OutParm) || Prop->HasAnyPropertyFlags(CPF_ReturnParm))
                        continue;
                        
                    void* ValuePtr = Prop->ContainerPtrToValuePtr<void>(Params);
                    FString ParamName = Prop->GetName();
                    
                    // Convert property to JSON
                    TSharedPtr<FJsonValue> JsonValue = GetJsonValueFromProperty(Prop, ValuePtr);
                    if (JsonValue.IsValid())
                    {
                    	Results[ParamName] = JsonValue;
                    }
                }
                
                // Clean up
                for (TFieldIterator<FProperty> It(Function); It && (It->PropertyFlags & CPF_Parm); ++It)
                {
                    It->DestroyValue_InContainer(Params);
                }
                FMemory::Free(Params);

                // Return the results object directly
                return Results.GetJsonValue();
            }

            break;
        }
    }

	return SimpleJSON("{ \"error\" : \"no matching query found\" }").GetJsonValue();
}

// Helper function to set property value from JSON
bool USageExtension::SetPropertyValueFromJson(FProperty* Prop, void* ValuePtr, const TSharedPtr<FJsonValue>& JsonValue)
{
    // Handle different property types
    if (Prop->IsA<FStrProperty>())
    {
        FString Value = JsonValue->AsString();
        CastField<FStrProperty>(Prop)->SetPropertyValue(ValuePtr, Value);
        return true;
    }
    else if (Prop->IsA<FNameProperty>())
    {
        FString Value = JsonValue->AsString();
        CastField<FNameProperty>(Prop)->SetPropertyValue(ValuePtr, FName(*Value));
        return true;
    }
    else if (Prop->IsA<FTextProperty>())
    {
        FString Value = JsonValue->AsString();
        CastField<FTextProperty>(Prop)->SetPropertyValue(ValuePtr, FText::FromString(Value));
        return true;
    }
    else if (Prop->IsA<FIntProperty>())
    {
        int32 Value = (int32)JsonValue->AsNumber();
        CastField<FIntProperty>(Prop)->SetPropertyValue(ValuePtr, Value);
        return true;
    }
    else if (Prop->IsA<FFloatProperty>())
    {
        float Value = (float)JsonValue->AsNumber();
        CastField<FFloatProperty>(Prop)->SetPropertyValue(ValuePtr, Value);
        return true;
    }
    else if (Prop->IsA<FDoubleProperty>())
    {
        double Value = JsonValue->AsNumber();
        CastField<FDoubleProperty>(Prop)->SetPropertyValue(ValuePtr, Value);
        return true;
    }
    else if (Prop->IsA<FBoolProperty>())
    {
        bool Value = JsonValue->AsBool();
        CastField<FBoolProperty>(Prop)->SetPropertyValue(ValuePtr, Value);
        return true;
    }
    else if (Prop->IsA<FStructProperty>())
    {
        FStructProperty* StructProp = CastField<FStructProperty>(Prop);
        
        // Handle Vector
        if (StructProp->Struct->GetFName() == NAME_Vector)
        {
            FVector Vector;
            if (JsonValue->Type == EJson::Array)
            {
                const TArray<TSharedPtr<FJsonValue>>& VectorArray = JsonValue->AsArray();
                if (VectorArray.Num() >= 3)
                {
                    Vector.X = (float)VectorArray[0]->AsNumber();
                    Vector.Y = (float)VectorArray[1]->AsNumber();
                    Vector.Z = (float)VectorArray[2]->AsNumber();
                    *reinterpret_cast<FVector*>(ValuePtr) = Vector;
                    return true;
                }
            }
            else if (JsonValue->Type == EJson::Object)
            {
                const TSharedPtr<FJsonObject>& VectorObj = JsonValue->AsObject();
                double X, Y, Z;
                if (VectorObj->TryGetNumberField(TEXT("x"), X) &&
                    VectorObj->TryGetNumberField(TEXT("y"), Y) &&
                    VectorObj->TryGetNumberField(TEXT("z"), Z))
                {
                    Vector.X = (float)X;
                    Vector.Y = (float)Y;
                    Vector.Z = (float)Z;
                    *reinterpret_cast<FVector*>(ValuePtr) = Vector;
                    return true;
                }
            }
        }
        // Handle Color
        else if (StructProp->Struct->GetFName() == NAME_Color || 
                 StructProp->Struct->GetFName() == NAME_LinearColor)
        {
            FLinearColor Color;
            if (JsonValue->Type == EJson::Array)
            {
                const TArray<TSharedPtr<FJsonValue>>& ColorArray = JsonValue->AsArray();
                if (ColorArray.Num() >= 3)
                {
                    Color.R = (float)ColorArray[0]->AsNumber();
                    Color.G = (float)ColorArray[1]->AsNumber();
                    Color.B = (float)ColorArray[2]->AsNumber();
                    Color.A = ColorArray.Num() >= 4 ? (float)ColorArray[3]->AsNumber() : 1.0f;
                    
                    if (StructProp->Struct->GetFName() == NAME_LinearColor)
                    {
                        *reinterpret_cast<FLinearColor*>(ValuePtr) = Color;
                    }
                    else // NAME_Color
                    {
                        *reinterpret_cast<FColor*>(ValuePtr) = Color.ToFColor(true);
                    }
                    return true;
                }
            }
            else if (JsonValue->Type == EJson::Object)
            {
                const TSharedPtr<FJsonObject>& ColorObj = JsonValue->AsObject();
                double R, G, B, A = 1.0;
                if (ColorObj->TryGetNumberField(TEXT("r"), R) &&
                    ColorObj->TryGetNumberField(TEXT("g"), G) &&
                    ColorObj->TryGetNumberField(TEXT("b"), B))
                {
                    ColorObj->TryGetNumberField(TEXT("a"), A); // Alpha is optional
                    Color.R = (float)R;
                    Color.G = (float)G;
                    Color.B = (float)B;
                    Color.A = (float)A;
                    
                    if (StructProp->Struct->GetFName() == NAME_LinearColor)
                    {
                        *reinterpret_cast<FLinearColor*>(ValuePtr) = Color;
                    }
                    else // NAME_Color
                    {
                        *reinterpret_cast<FColor*>(ValuePtr) = Color.ToFColor(true);
                    }
                    return true;
                }
            }
        }
        // Handle Rotator
        else if (StructProp->Struct->GetFName() == NAME_Rotator)
        {
            FRotator Rotator;
            if (JsonValue->Type == EJson::Array)
            {
                const TArray<TSharedPtr<FJsonValue>>& RotatorArray = JsonValue->AsArray();
                if (RotatorArray.Num() >= 3)
                {
                    Rotator.Pitch = (float)RotatorArray[0]->AsNumber();
                    Rotator.Yaw = (float)RotatorArray[1]->AsNumber();
                    Rotator.Roll = (float)RotatorArray[2]->AsNumber();
                    *reinterpret_cast<FRotator*>(ValuePtr) = Rotator;
                    return true;
                }
            }
            else if (JsonValue->Type == EJson::Object)
            {
                const TSharedPtr<FJsonObject>& RotatorObj = JsonValue->AsObject();
                double Pitch, Yaw, Roll;
                if (RotatorObj->TryGetNumberField(TEXT("pitch"), Pitch) &&
                    RotatorObj->TryGetNumberField(TEXT("yaw"), Yaw) &&
                    RotatorObj->TryGetNumberField(TEXT("roll"), Roll))
                {
                    Rotator.Pitch = (float)Pitch;
                    Rotator.Yaw = (float)Yaw;
                    Rotator.Roll = (float)Roll;
                    *reinterpret_cast<FRotator*>(ValuePtr) = Rotator;
                    return true;
                }
            }
        }
        // Handle Transform
        else if (StructProp->Struct->GetFName() == NAME_Transform)
        {
            FTransform Transform;
            
            if (JsonValue->Type == EJson::Object)
            {
                const TSharedPtr<FJsonObject>& TransformObj = JsonValue->AsObject();
                
                // Handle location
                const TSharedPtr<FJsonObject>* LocationObj;
                if (TransformObj->TryGetObjectField(TEXT("location"), LocationObj))
                {
                    FVector Location;
                    double X, Y, Z;
                    if (LocationObj->Get()->TryGetNumberField(TEXT("x"), X) &&
                        LocationObj->Get()->TryGetNumberField(TEXT("y"), Y) &&
                        LocationObj->Get()->TryGetNumberField(TEXT("z"), Z))
                    {
                        Location.X = (float)X;
                        Location.Y = (float)Y;
                        Location.Z = (float)Z;
                        Transform.SetLocation(Location);
                    }
                }
                else
                {
                    const TArray<TSharedPtr<FJsonValue>>* LocationArray;
                    if (TransformObj->TryGetArrayField(TEXT("location"), LocationArray) && LocationArray->Num() >= 3)
                    {
                        FVector Location;
                        Location.X = (float)(*LocationArray)[0]->AsNumber();
                        Location.Y = (float)(*LocationArray)[1]->AsNumber();
                        Location.Z = (float)(*LocationArray)[2]->AsNumber();
                        Transform.SetLocation(Location);
                    }
                }
                
                // Handle rotation
                const TSharedPtr<FJsonObject>* RotationObj;
                if (TransformObj->TryGetObjectField(TEXT("rotation"), RotationObj))
                {
                    FQuat Rotation;
                    double X, Y, Z, W;
                    if (RotationObj->Get()->TryGetNumberField(TEXT("x"), X) &&
                        RotationObj->Get()->TryGetNumberField(TEXT("y"), Y) &&
                        RotationObj->Get()->TryGetNumberField(TEXT("z"), Z) &&
                        RotationObj->Get()->TryGetNumberField(TEXT("w"), W))
                    {
                        Rotation.X = (float)X;
                        Rotation.Y = (float)Y;
                        Rotation.Z = (float)Z;
                        Rotation.W = (float)W;
                        Transform.SetRotation(Rotation);
                    }
                    else
                    {
                        // Try Euler angles (pitch, yaw, roll)
                        double Pitch, Yaw, Roll;
                        if (RotationObj->Get()->TryGetNumberField(TEXT("pitch"), Pitch) &&
                            RotationObj->Get()->TryGetNumberField(TEXT("yaw"), Yaw) &&
                            RotationObj->Get()->TryGetNumberField(TEXT("roll"), Roll))
                        {
                            FRotator Rotator((float)Pitch, (float)Yaw, (float)Roll);
                            Transform.SetRotation(Rotator.Quaternion());
                        }
                    }
                }
                else
                {
                    const TArray<TSharedPtr<FJsonValue>>* RotationArray;
                    if (TransformObj->TryGetArrayField(TEXT("rotation"), RotationArray))
                    {
                        if (RotationArray->Num() >= 4) // Quaternion [x, y, z, w]
                        {
                            FQuat Rotation;
                            Rotation.X = (float)(*RotationArray)[0]->AsNumber();
                            Rotation.Y = (float)(*RotationArray)[1]->AsNumber();
                            Rotation.Z = (float)(*RotationArray)[2]->AsNumber();
                            Rotation.W = (float)(*RotationArray)[3]->AsNumber();
                            Transform.SetRotation(Rotation);
                        }
                        else if (RotationArray->Num() >= 3) // Euler angles [pitch, yaw, roll]
                        {
                            FRotator Rotator;
                            Rotator.Pitch = (float)(*RotationArray)[0]->AsNumber();
                            Rotator.Yaw = (float)(*RotationArray)[1]->AsNumber();
                            Rotator.Roll = (float)(*RotationArray)[2]->AsNumber();
                            Transform.SetRotation(Rotator.Quaternion());
                        }
                    }
                }
                
                // Handle scale
                const TSharedPtr<FJsonObject>* ScaleObj;
                if (TransformObj->TryGetObjectField(TEXT("scale"), ScaleObj))
                {
                    FVector Scale;
                    double X, Y, Z;
                    if (ScaleObj->Get()->TryGetNumberField(TEXT("x"), X) &&
                        ScaleObj->Get()->TryGetNumberField(TEXT("y"), Y) &&
                        ScaleObj->Get()->TryGetNumberField(TEXT("z"), Z))
                    {
                        Scale.X = (float)X;
                        Scale.Y = (float)Y;
                        Scale.Z = (float)Z;
                        Transform.SetScale3D(Scale);
                    }
                }
                else
                {
                    const TArray<TSharedPtr<FJsonValue>>* ScaleArray;
                    if (TransformObj->TryGetArrayField(TEXT("scale"), ScaleArray) && ScaleArray->Num() >= 3)
                    {
                        FVector Scale;
                        Scale.X = (float)(*ScaleArray)[0]->AsNumber();
                        Scale.Y = (float)(*ScaleArray)[1]->AsNumber();
                        Scale.Z = (float)(*ScaleArray)[2]->AsNumber();
                        Transform.SetScale3D(Scale);
                    }
                }
                
                *reinterpret_cast<FTransform*>(ValuePtr) = Transform;
                return true;
            }
        }
        // Handle custom structs
        else if (JsonValue->Type == EJson::Object)
        {
            const TSharedPtr<FJsonObject>& StructObj = JsonValue->AsObject();
            
            // Get the struct's UScriptStruct
            UScriptStruct* ScriptStruct = StructProp->Struct;
            
            // Iterate through the struct's properties
            for (TFieldIterator<FProperty> PropIt(ScriptStruct); PropIt; ++PropIt)
            {
                FProperty* StructMemberProp = *PropIt;
                FString PropName = StructMemberProp->GetAuthoredName();
                
                // Get the property's value from JSON
                if (const TSharedPtr<FJsonValue> PropValue = StructObj->TryGetField(PropName); PropValue.IsValid())
                {
                    // Calculate the property's address within the struct
                    void* PropPtr = StructMemberProp->ContainerPtrToValuePtr<void>(ValuePtr);
                    
                    // Recursively set the property value
                    SetPropertyValueFromJson(StructMemberProp, PropPtr, PropValue);
                }
            }
            
            return true;
        }
    }
    
    return false;
}

// Helper function to get JSON value from property
TSharedPtr<FJsonValue> USageExtension::GetJsonValueFromProperty(FProperty* Prop, void* ValuePtr)
{
    // Handle different property types
    if (Prop->IsA<FStrProperty>())
    {
        FString Value = CastField<FStrProperty>(Prop)->GetPropertyValue(ValuePtr);
        return MakeShared<FJsonValueString>(Value);
    }
    else if (Prop->IsA<FNameProperty>())
    {
        FName Value = CastField<FNameProperty>(Prop)->GetPropertyValue(ValuePtr);
        return MakeShared<FJsonValueString>(Value.ToString());
    }
    else if (Prop->IsA<FTextProperty>())
    {
        FText Value = CastField<FTextProperty>(Prop)->GetPropertyValue(ValuePtr);
        return MakeShared<FJsonValueString>(Value.ToString());
    }
    else if (Prop->IsA<FIntProperty>())
    {
        int32 Value = CastField<FIntProperty>(Prop)->GetPropertyValue(ValuePtr);
        return MakeShared<FJsonValueNumber>(Value);
    }
    else if (Prop->IsA<FFloatProperty>())
    {
        float Value = CastField<FFloatProperty>(Prop)->GetPropertyValue(ValuePtr);
        return MakeShared<FJsonValueNumber>(Value);
    }
    else if (Prop->IsA<FDoubleProperty>())
    {
        double Value = CastField<FDoubleProperty>(Prop)->GetPropertyValue(ValuePtr);
        return MakeShared<FJsonValueNumber>(Value);
    }
    else if (Prop->IsA<FBoolProperty>())
    {
        bool Value = CastField<FBoolProperty>(Prop)->GetPropertyValue(ValuePtr);
        return MakeShared<FJsonValueBoolean>(Value);
    }
    else if (Prop->IsA<FArrayProperty>())
    {
        FArrayProperty* ArrayProp = CastField<FArrayProperty>(Prop);
        FScriptArrayHelper ArrayHelper(ArrayProp, ValuePtr);
        
        TArray<TSharedPtr<FJsonValue>> JsonArray;
        for (int32 i = 0; i < ArrayHelper.Num(); i++)
        {
            void* ElementPtr = ArrayHelper.GetRawPtr(i);
            TSharedPtr<FJsonValue> ElementValue = GetJsonValueFromProperty(ArrayProp->Inner, ElementPtr);
            if (ElementValue.IsValid())
            {
                JsonArray.Add(ElementValue);
            }
        }
        
        return MakeShared<FJsonValueArray>(JsonArray);
    }
    else if (Prop->IsA<FStructProperty>())
    {
        FStructProperty* StructProp = CastField<FStructProperty>(Prop);
        
        // Handle Vector
        if (StructProp->Struct->GetFName() == NAME_Vector)
        {
            FVector Vector = *reinterpret_cast<FVector*>(ValuePtr);
            TSharedPtr<FJsonObject> VectorObj = MakeShared<FJsonObject>();
            VectorObj->SetNumberField(TEXT("x"), Vector.X);
            VectorObj->SetNumberField(TEXT("y"), Vector.Y);
            VectorObj->SetNumberField(TEXT("z"), Vector.Z);
            return MakeShared<FJsonValueObject>(VectorObj);
        }
        // Handle Color
        else if (StructProp->Struct->GetFName() == NAME_LinearColor)
        {
            FLinearColor Color = *reinterpret_cast<FLinearColor*>(ValuePtr);
            TSharedPtr<FJsonObject> ColorObj = MakeShared<FJsonObject>();
            ColorObj->SetNumberField(TEXT("r"), Color.R);
            ColorObj->SetNumberField(TEXT("g"), Color.G);
            ColorObj->SetNumberField(TEXT("b"), Color.B);
            ColorObj->SetNumberField(TEXT("a"), Color.A);
            return MakeShared<FJsonValueObject>(ColorObj);
        }
        else if (StructProp->Struct->GetFName() == NAME_Color)
        {
            FColor Color = *reinterpret_cast<FColor*>(ValuePtr);
            FLinearColor LinearColor = FLinearColor(Color);
            TSharedPtr<FJsonObject> ColorObj = MakeShared<FJsonObject>();
            ColorObj->SetNumberField(TEXT("r"), LinearColor.R);
            ColorObj->SetNumberField(TEXT("g"), LinearColor.G);
            ColorObj->SetNumberField(TEXT("b"), LinearColor.B);
            ColorObj->SetNumberField(TEXT("a"), LinearColor.A);
            return MakeShared<FJsonValueObject>(ColorObj);
        }
        // Handle Rotator
        else if (StructProp->Struct->GetFName() == NAME_Rotator)
        {
            FRotator Rotator = *reinterpret_cast<FRotator*>(ValuePtr);
            TSharedPtr<FJsonObject> RotatorObj = MakeShared<FJsonObject>();
            RotatorObj->SetNumberField(TEXT("pitch"), Rotator.Pitch);
            RotatorObj->SetNumberField(TEXT("yaw"), Rotator.Yaw);
            RotatorObj->SetNumberField(TEXT("roll"), Rotator.Roll);
            return MakeShared<FJsonValueObject>(RotatorObj);
        }
    }
    
    return nullptr;
}

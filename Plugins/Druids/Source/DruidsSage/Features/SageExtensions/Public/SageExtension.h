#pragma once

#include "CoreMinimal.h"
#include "EditorUtilityObject.h"

#include "SageTypes.h"
#include "SageExtensionTypes.h"

#include "SageExtension.generated.h"

UCLASS(Blueprintable, meta = (DisplayName = "Sage Extension"))
class SAGEEXTENSIONS_API USageExtension : public UEditorUtilityObject
{
    GENERATED_BODY()

private:
    static FString BuildFullPath(EDruidsSagePathPrefix Prefix, const FString& ModuleName, const FString& ClassName);

public:
    USageExtension(): PathPrefix()
    {
    }

    void RefreshExtension();

    TSharedPtr<FDruidsSageExtensionDefinition> GetExtensionDefinition() const;

protected:

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sage Extension")
    FDruidsSageExtensionDefinition Definition;

public:
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sage Extension")
    bool Enabled = true;
    
private:
    // Hide these properties as they'll be managed internally
    UPROPERTY()
    EDruidsSagePathPrefix PathPrefix;

    UPROPERTY()
    FString ModuleName;

    UPROPERTY()
    FString ClassName;

    UPROPERTY()
    TMap<FString, FDruidsSageExtensionAssetTypeData> AssetTypeMap;

public:
    UFUNCTION(BlueprintCallable, Category = "Sage Extension")
    void DiscoverActionsAndQueries();

    /** Returns the currently active asset in the editor, or null if no asset is being edited */
    UFUNCTION(BlueprintCallable, Category = "Sage Extension", Meta = (DisplayName = "Get Currently Edited Asset"))
    static UObject* GetCurrentlyEditedAsset();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Sage Extension")
    static UWorld* GetWorldEditorContext();

    /** Report an error from a Blueprint extension function */
    UFUNCTION(BlueprintCallable, Category = "Sage Extension")
    static void ReportExtensionError(const FString& ErrorMessage);

    // Returns true if this extension should be active for the given context
    virtual bool MatchesContext(TWeakObjectPtr<> ActiveObject) const;

    virtual void ExecuteAction(TWeakPtr<FJsonObject> ActionRequestDetails);
    virtual TSharedPtr<FJsonValue> ExecuteQuery(TWeakPtr<FJsonObject> QueryRequestDetails);

private:
    void DiscoverFunction(UFunction* Function, const TMap<FString, FString>& ExistingDescriptions);

public:
    virtual void PostInitProperties() override;

    virtual void PostCDOCompiled(const FPostCDOCompiledContext& Context) override;

    void PopulateAssetTypes();

    UFUNCTION(CallInEditor, Category = "Sage Extension")
    TArray<FString> GetAvailableAssetTypes() const;

    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;

    // Helper function to set property value from JSON
    static bool SetPropertyValueFromJson(FProperty* Prop, void* ValuePtr, const TSharedPtr<FJsonValue>& JsonValue);

    // Helper function to get array inner type
    static EDruidsSageExtensionParameterType GetArrayInnerType(const FArrayProperty* ArrayProperty);

    // Helper function to get JSON value from property
    TSharedPtr<FJsonValue> GetJsonValueFromProperty(FProperty* Prop, void* ValuePtr);

    // Error reporting functions
    static void ClearExtensionError();
    static FString GetExtensionError();
    static bool HasExtensionError();

private:
    // Thread-local storage for extension errors
    static thread_local FString CurrentExtensionError;
};
